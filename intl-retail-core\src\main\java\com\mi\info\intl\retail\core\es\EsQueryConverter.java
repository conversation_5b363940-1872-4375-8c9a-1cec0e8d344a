package com.mi.info.intl.retail.core.es;

import org.elasticsearch.index.query.*;
import org.springframework.data.elasticsearch.core.query.*;

import java.lang.reflect.Field;
import java.util.*;

public class EsQueryConverter {

    public static BoolQueryBuilder convert(Object paramObj) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Arrays.stream(paramObj.getClass().getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(EsQuery.class))
                .forEach(field -> processField(boolQuery, field, paramObj));
        return  boolQuery;
    }

    private static void processField(BoolQueryBuilder boolQuery, Field field, Object paramObj) {
        try {
            field.setAccessible(true);
            EsQuery annotation = field.getAnnotation(EsQuery.class);
            Object value = field.get(paramObj);

            if (value != null || annotation.required()) {
                boolQuery.must(buildQuery(annotation, value));
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Error processing field", e);
        }
    }

    private static AbstractQueryBuilder buildQuery(EsQuery annotation, Object value) {
        switch (annotation.queryType()) {
            case TERM:
                return QueryBuilders.termQuery(annotation.field(), value);
            case TERMS: // 新增TERMS类型处理
                return QueryBuilders.termsQuery(annotation.field(), convertToArray(value));
            case MATCH:
                return buildMatchQuery(annotation, value);
            case RANGE:
                return buildRangeQuery(annotation.field(), (Comparable[]) value);
            case WILDCARD:
                return QueryBuilders.wildcardQuery(annotation.field(), "*" + value + "*");
            default:
                throw new IllegalArgumentException("unknown query type");
        }
    }

    // 处理数组/集合类型的TERMS查询参数
    private static Object[] convertToArray(Object value) {
        if (value instanceof Collection) {
            return ((Collection<?>) value).toArray();
        } else if (value.getClass().isArray()) {
            return (Object[]) value;
        }
        throw new IllegalArgumentException("value must be an array or collection");
    }

    private static MatchQueryBuilder buildMatchQuery(EsQuery annotation, Object value) {
        MatchQueryBuilder builder = QueryBuilders.matchQuery(annotation.field(), value);
        if (!annotation.analyzer().isEmpty()) {
            builder.analyzer(annotation.analyzer());
        }
        return builder;
    }

    private static RangeQueryBuilder buildRangeQuery(String field, Comparable[] range) {
        if (range.length != 2) throw new IllegalArgumentException("range must have exactly two elements");
        return QueryBuilders.rangeQuery(field).gte(range[0]).lte(range[1]);
    }
}
