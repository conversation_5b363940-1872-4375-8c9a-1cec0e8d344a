package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 可售SKU查询响应
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Data
public class GetSkuListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品线数组
     */
    private List<ProductLineInfo> productLineList;

    /**
     * 产品简称数组
     */
    private List<ShortNameInfo> shortNameList;

    /**
     * 产品数组
     */
    private List<ProductInfo> productList;

    /**
     * 产品线信息
     */
    @Data
    public static class ProductLineInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 产品线名称
         */
        private String name;
    }

    /**
     * 产品简称信息
     */
    @Data
    public static class ShortNameInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 产品简称
         */
        private String name;
    }

    /**
     * 产品信息
     */
    @Data
    public static class ProductInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 可售SKU id
         */
        private Integer id;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 产品ID（五位数，非系统自增主键）
         */
        private Long productCode;

        /**
         * 产品线
         */
        private String productLine;

        /**
         * 产品简称
         */
        private String shortName;
    }
} 