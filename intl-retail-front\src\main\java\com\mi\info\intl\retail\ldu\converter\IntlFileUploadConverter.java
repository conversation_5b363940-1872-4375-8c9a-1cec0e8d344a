package com.mi.info.intl.retail.ldu.converter;

import com.mi.info.intl.retail.api.file.dto.IntlFileUploadDto;
import com.mi.info.intl.retail.converter.AbstractMapstructConverter;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 功能描述：File Upload转换类
 *
 * <AUTHOR>
 * @date 2025/8/1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IntlFileUploadConverter extends AbstractMapstructConverter<IntlFileUpload, IntlFileUploadDto> {

}
