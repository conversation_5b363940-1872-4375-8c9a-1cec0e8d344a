package com.mi.info.intl.retail.intlretail.service.api.so.sales.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 功能描述：Sales Imei 返回dto
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class SalesImeiRespDto implements Serializable {

    private static final long serialVersionUID = -4765818862783517060L;

    @ApiDocClassDefine("id")
    private Long id;

    @ApiDocClassDefine("销售人mid")
    private Long salesmanMid;

    @ApiDocClassDefine("英文名称")
    private String salesmanName;

    @ApiDocClassDefine("销售人员关联的 rms 账户")
    private String salesmanAccount;

    @ApiDocClassDefine("创建人的职位")
    private Integer salesmanTitle;
    @ApiDocClassDefine("创建人的职位描述")
    private String salesmanTitleDesc;

    @ApiDocClassDefine("门店编码")
    private String storeCode;
    @ApiDocClassDefine("门店名称")
    private String storeName;

    @ApiDocClassDefine("门店等级")
    private Integer storeGrade;
    @ApiDocClassDefine("门店等级描述")
    private String storeGradeDesc;

    @ApiDocClassDefine("门店类型")
    private Integer storeType;
    @ApiDocClassDefine("门店类型描述")
    private String storeTypeDesc;

    @ApiDocClassDefine("门店渠道类型（线上/线下等，需结合业务明确）")
    private Integer channelType;
    @ApiDocClassDefine("门店渠道类型描述")
    private String channelTypeDesc;

    @ApiDocClassDefine("门店是否含 SR（0：否；1：是）")
    private Integer hasSr;
    @ApiDocClassDefine("门店是否含SR描述")
    private String hasSrDesc;
    @ApiDocClassDefine("门店是否含 PC（0：否；1：是）")
    private Integer hasPc;
    @ApiDocClassDefine("门店是否含PC描述")
    private String hasPcDesc;

    @ApiDocClassDefine("RMS阵地code")
    private String positionCode;
    @ApiDocClassDefine("阵地名称")
    private String positionName;
    @ApiDocClassDefine("位置类型（如 仓库、展厅 等）")
    private Integer positionType;
    @ApiDocClassDefine("位置类型描述")
    private String positionTypeDesc;

    @ApiDocClassDefine("关联零售商ID")
    private String retailerCode;
    @ApiDocClassDefine("RMS零售商Code")
    private String retailerName;
    @ApiDocClassDefine("国家/地区标签")
    private String country;
    @ApiDocClassDefine("国家/地区编码")
    private String countryCode;
    @ApiDocClassDefine("省标签")
    private String province;
    @ApiDocClassDefine("省编码")
    private String provinceCode;
    @ApiDocClassDefine("城市标签")
    private String city;
    @ApiDocClassDefine("城市编码")
    private String cityCode;

    @ApiDocClassDefine("IMEI1")
    private String imei1;
    @ApiDocClassDefine("IMEI2")
    private String imei2;
    @ApiDocClassDefine("SN")
    private String sn;

    @ApiDocClassDefine("产品 code")
    private Long productId;
    @ApiDocClassDefine("激活时间")
    private Long activationTime;

    @ApiDocClassDefine("销售时间")
    private Long salesTime;

    @ApiDocClassDefine("验证结果")
    private Integer verificationResult;
    @ApiDocClassDefine("验证结果描述")
    private String verificationResultDesc;
    @ApiDocClassDefine("验证结果详情")
    private String resultDetail;
    @ApiDocClassDefine("Repeat User")
    private String repeatUser;

    @ApiDocClassDefine("SKU名称")
    private String skuName;
    @ApiDocClassDefine("SPU英文")
    private String spuEn;

    @ApiDocClassDefine("产品线EN")
    private String productLineEn;
    @ApiDocClassDefine("产品线EN描述")
    private String productLineEnDesc;

    @ApiDocClassDefine("rrp")
    private BigDecimal rrp;
    @ApiDocClassDefine("currency")
    private String currency;

    @ApiDocClassDefine("上报类型")
    private Integer reportingType;
    @ApiDocClassDefine("上报类型描述")
    private String reportingTypeDesc;

    @ApiDocClassDefine("图片存在标识：0:不存在，1：存在")
    private Integer isPhotoExist;
    @ApiDocClassDefine("备注")
    private String remark;

    @ApiDocClassDefine("创建人")
    private Long createdBy;
    @ApiDocClassDefine("创建人描述")
    private String createdByDesc;

    @ApiDocClassDefine("创建时间毫秒时间戳")
    private Long createdOn;


}
