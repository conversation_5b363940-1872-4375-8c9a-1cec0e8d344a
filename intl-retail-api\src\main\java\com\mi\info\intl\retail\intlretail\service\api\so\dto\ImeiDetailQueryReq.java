package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IMEI明细页查询请求参数
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@Data
public class ImeiDetailQueryReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ImeiId
     */
    @JsonProperty("imeiId")
    private String imeiId;

    /**
     * 用户GUID
     */
    @JsonProperty("userId")
    private String userId;

    /**
     * 用户mid
     */
    @JsonProperty("miId")
    private Long miId;

    /**
     * 职位code
     */
    @JsonProperty("userTitle")
    private Long userTitle;
}
