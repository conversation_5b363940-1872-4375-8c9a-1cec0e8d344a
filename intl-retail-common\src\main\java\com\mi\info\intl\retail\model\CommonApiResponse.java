package com.mi.info.intl.retail.model;

import lombok.Getter;

import java.io.Serializable;

@Getter
public class CommonApiResponse<T> implements Serializable {
    private static final long serialVersionUID = -153195758211395798L;
    private int code;
    private String message;
    private T data;

    public CommonApiResponse(T body) {
        this.code = 0;
        this.message = "ok";
        this.data = body;
    }

    public CommonApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> CommonApiResponse<T> failure(int code, String message) {
        return new CommonApiResponse<>(code, message, null);
    }

    public static <T> CommonApiResponse<T> failure(int code, String message, T data) {
        return new CommonApiResponse<>(code, message, data);
    }

    public static <T> CommonApiResponse<T> success(T data) {
        return new CommonApiResponse<>(data);
    }
}

