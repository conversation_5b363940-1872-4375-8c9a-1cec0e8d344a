package com.mi.info.intl.retail.ldu.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一状态码枚举
 * 规范：成功码从0开始，错误码从1开始递增
 */
@Getter
@AllArgsConstructor
public enum ResultCodeEnum {

    /* 成功状态码 */
    SUCCESS(0, "成功", "Success"),

    /* 通用错误码 1xx */
    PARAM_ERROR(100, "参数错误", "Parameter error"),
    SYSTEM_ERROR(101, "系统异常", "System error"),
    DATA_NOT_FOUND(102, "数据不存在", "Data not found"),
    REPEAT_OPERATION(103, "重复数据", "Repeat Data"),
    DATA_MISS(104, "数据不存值", "Data is missing"),

    /* 业务错误码 2xx */
    TASK_EXEC_FAILED(200, "任务执行失败", "Task execution failed"),
    EXPORT_FAILED(201, "导出失败", "Export failed"),
    FILE_UPLOAD_FAILED(202, "文件上传失败", "File upload failed"),

    /* 第三方服务错误码 3xx */
    RPC_CALL_FAILED(300, "远程调用失败", "RPC call failed"),
    DB_OPERATION_FAILED(301, "数据库操作失败", "Database operation failed");

    /**
     * 状态码
     */
    private final Integer code;
    /**
     * 中文错误信息
     */
    private final String zhMsg;
    /**
     * 英文错误信息
     */
    private final String enMsg;

    /**
     * 根据code获取枚举（避免遍历values()）
     */
}
