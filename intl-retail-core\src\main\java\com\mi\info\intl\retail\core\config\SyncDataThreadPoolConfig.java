package com.mi.info.intl.retail.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class SyncDataThreadPoolConfig {

    @Bean(name = "syncDataThreadPool")
    public ThreadPoolTaskExecutor syncDataThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数 (根据CPU核心数调整)
        executor.setCorePoolSize(5);

        // 最大线程数 (核心线程数的2-3倍)
        executor.setMaxPoolSize(10);

        // 队列容量 (根据业务量调整)
        executor.setQueueCapacity(1000);

        // 线程名前缀
        executor.setThreadNamePrefix("intl-retial-excutor-");

        // 拒绝策略 (建议使用CallerRunsPolicy让主线程执行)
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 线程空闲存活时间(秒)
        executor.setKeepAliveSeconds(60);

        // 等待所有任务完成后关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待终止的最大时间(秒)
        executor.setAwaitTerminationSeconds(60);

        // 初始化
        executor.initialize();

        return executor;
    }
}
