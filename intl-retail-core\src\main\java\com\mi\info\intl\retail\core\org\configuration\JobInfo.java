package com.mi.info.intl.retail.core.org.configuration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class JobInfo {
    /**
     * 组织场景
     */
    private String scene;
    /**
     * 组织编码
     */
    private String organCode;
    /**
     * 岗位id
     */
    private Integer positionId;
    /**
     * 岗位类型列表
     */
    private List<Integer> manageChannelList;

}
