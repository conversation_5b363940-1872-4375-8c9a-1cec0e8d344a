package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class IntlLduReportLogDto implements Serializable {

    private static final long serialVersionUID = -765432109876543210L;

    private Long id;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家/地区编码
     */
    private String countryCode;

    /**
     * 零售商编码
     */
    private String retailerCode;

    /**
     * 零售商名称
     */
    private String retailerName;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 图片路径/URL
     */
    private List<String> imageUrlList;

    /**
     * 产品线
     */
    private String productLine;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * RAM容量
     */
    private String ramCapacity;

    /**
     * ROM容量
     */
    private String romCapacity;

    /**
     * 序列号SN
     */
    private String sn;

    /**
     * 69Code
     */
    private String code69;

    /**
     * IMEI 1
     */
    private String imei1;

    /**
     * IMEI 2
     */
    private String imei2;

    /**
     * 在计划内：带值为Yes不在：带值为No
     */
    private Integer planStatus;

    private String planStatusName;

    /**
     * LDU类型:大货（Mass Production Version）、专样（Customized Version）
     */
    private String lduType;

    /**
     * 有安装：带值为Yes 未安装：带值为No
     */
    private Integer mishowStatus;

    private String mishowStatusName;

    /**
     * 最后一次成功获取Mishow信息的时间，精确到毫秒
     */
    private Long lastMishowFetchTime;

    /**
     * 展陈中：上报后的默认状态 已丢失：LDU上报数据修改为丢失后 已损坏：LDU上报数据修改为损坏后 已更换：LDU上报数据修改为更换后
     */
    private Integer displayStatus;

    private String displayStatusName;

    /**
     * 上报距离差
     */
    private BigDecimal reportDistance;

    /**
     * 创建人ID/上报人ID
     */
    private String createUserId;

    /**
     * 创建人姓名/上报人姓名
     */
    private String createUserName;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 创建时间/上报时间
     */
    private Long createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新名字
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 颜色
     */
    private String color;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件信息
     */
    private List<IntlFileUploadDto> fileUploadList;

}
