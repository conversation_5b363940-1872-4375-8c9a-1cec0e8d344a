package com.mi.info.intl.retail.ldu.enums;

import lombok.Getter;

@Getter
public enum LanguageEnum {

    /**
     * 语言枚举
     */
    ZH_CN("zh-CN", "简体中文", "Simplified Chinese"),
    EN_US("en-US", "英文（美国）", "English (United States)");

    private final String code;
    private final String zhName;
    private final String enName;

    LanguageEnum(String code, String zhName, String enName) {
        this.code = code;
        this.zhName = zhName;
        this.enName = enName;
    }


    /** 根据语言代码查找枚举 */
    public static LanguageEnum fromCode(String code) {
        for (LanguageEnum lang : values()) {
            if (lang.code.equalsIgnoreCase(code)) {
                return lang;
            }
        }
        throw new IllegalArgumentException("未知语言代码: " + code);
    }

}