package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 序列号黑名单请求参数
 *
 * <AUTHOR>
 * @date 2025/7/25
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SnBlackRequest extends BasePageRequest  implements Serializable {
    
    private static final long serialVersionUID = 5983264805107402235L;

    /**
     * 黑名单ID
     */
    @ApiDocClassDefine(value = "黑名单ID")
    private String id;

    /**
     * 国家代码code
     */
    @ApiDocClassDefine(value = "国家代码code")
    private List<String> countryCodeList;

    /**
     * 黑名单类型
     */
    @ApiDocClassDefine(value = "黑名单类型")
    private List<String> typeCodeList;

    /**
     * 黑名单序列号
     */
    @ApiDocClassDefine(value = "黑名单序列号")
    private String snImei;

    /**
     * 状态
     */
    @ApiDocClassDefine(value = "状态")
    private List<String> statusList;

    /**
     * 创建时间,时间戳
     */
    @ApiDocClassDefine(value = "创建时间,时间戳")
    private Long createdOn;

    /**
     * 创建人
     */
    @ApiDocClassDefine(value = "创建人")
    private String createdBy;
}
