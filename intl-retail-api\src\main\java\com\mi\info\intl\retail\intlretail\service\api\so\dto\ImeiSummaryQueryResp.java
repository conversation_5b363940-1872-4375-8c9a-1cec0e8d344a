package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IMEI汇总数据查询响应参数
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@Data
public class ImeiSummaryQueryResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总数量
     */
    @JsonProperty("totalCount")
    private Integer totalCount;

    /**
     * 成功数量
     */
    @JsonProperty("successCount")
    private Integer successCount;

    /**
     * 校验数量
     */
    @JsonProperty("veriftingCount")
    private Integer veriftingCount;

    /**
     * 失败数量
     */
    @JsonProperty("failedCount")
    private Integer failedCount;
}
