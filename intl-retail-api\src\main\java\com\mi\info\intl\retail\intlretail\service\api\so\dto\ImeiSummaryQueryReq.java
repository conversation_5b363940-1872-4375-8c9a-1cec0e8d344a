package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IMEI汇总数据查询请求参数
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@Data
public class ImeiSummaryQueryReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 国家短代码
     */
    @JsonProperty("countryCode")
    private String countryCode;

    /**
     * 用户GUID
     */
    @JsonProperty("userId")
    private String userId;

    /**
     * 用户mid
     */
    @JsonProperty("miId")
    private Long miId;

    /**
     * 职位code
     */
    @JsonProperty("userTitle")
    private Long userTitle;

    /**
     * 模糊查询，支持IMEI/SN, product name
     */
    @JsonProperty("search")
    private String search;

    /**
     * 门店code
     */
    @JsonProperty("storeCode")
    private String storeCode;

    /**
     * 产品线
     */
    @JsonProperty("productLine")
    private Long productLine;

    /**
     * 门店类型
     */
    @JsonProperty("storeType")
    private Integer storeType;

    /**
     * 门店渠道类型
     */
    @JsonProperty("channelType")
    private Integer channelType;

    /**
     * IMEI校验状态
     */
    @JsonProperty("verifyResult")
    private Integer verifyResult;

    /**
     * 日期筛选类型
     * 1表示年月筛选，2表示自定义时间段筛选
     */
    @JsonProperty("dateFilterType")
    private String dateFilterType;

    /**
     * 销售时间(Sales Time)年份
     */
    @JsonProperty("year")
    private String year;

    /**
     * 销售时间(Sales Time)月份
     */
    @JsonProperty("month")
    private String month;

    /**
     * 销售时间(Sales Time)开始时间
     */
    @JsonProperty("startTime")
    private String startTime;

    /**
     * 销售时间(Sales Time)结束时间
     */
    @JsonProperty("endTime")
    private String endTime;
}
