package com.mi.info.intl.retail.ldu.util;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2025-07-16
 */
public class ConstantMessageTemplate {


    // 私有构造器，防止实例化
    private ConstantMessageTemplate() {
        throw new AssertionError("禁止实例化常量类");
    }

    // ========================== 业务相关常量模板 ==========================
    /**
     * SN重复上传提示（中文）- 参数：行号
     */
    public static final String SN_DUPLICATE_CN = "第{0}行该SN已存在，请勿重复上传！";
    public static final String SN_DUPLICATE_CN_2 = "该SN已存在，请勿重复上传！";
    /**
     * SN重复上传提示（英文）- 参数：行号
     */
    public static final String SN_DUPLICATE_EN = "Line {0}, the SN already exists, please don't repeat the upload!";
    public static final String SN_DUPLICATE_EN_2 = "the SN already exists, please don't repeat the upload!";

    /**
     * 小米产品SN/IMEI校验失败提示（中文）- 参数：行号
     */
    public static final String XIAOMI_SN_VALIDATION_FAILED_CN = "第{0}行SN/IMEI校验失败！非小米产品！";
    public static final String XIAOMI_SN_VALIDATION_FAILED_CN_2 = "SN/IMEI校验失败！非小米产品！";

    /**
     * 零售商和国家不匹配，校验失败提示
     */
    public static final String RETAILER_AND_COUNTRY_NO_MATH_CN = "第{0}行零售商和国家不匹配，请重新填写！";
    public static final String RETAILER_AND_COUNTRY_NO_MATH_CN2 = "所选零售商和国家不匹配，请重新选择";

    public static final String RETAILER_AND_COUNTRY_NO_MATH_EN = "line  {0}, retailer and country do not match, please re-film";
    public static final String RETAILER_AND_COUNTRY_NO_MATH_EN2 = "The selected retailer does not match the country, please reselect";

    public static final String PRODUCT_ID_NOT_EXIST_EN = "line  {0}, product id can not be empty!";

    public static final String TARGET_NUMS_NOT_EXIST_EN = "line  {0}, target coverage number can not be empty!";
    public static final String TARGET_NUMS_NOT_EXIST_CN = "第{0}行, 目标覆盖范围数不能为空！";

    public static final String TARGET_PRODUCT_NUMS_NOT_EXIST_EN = "line  {0}, target sample number can not be empty!";
    public static final String TARGET_PRODUCT_NUMS_NOT_EXIST_CN = "第{0}行, 目标出样数不能为空！";

    /**
     * 小米产品SN/IMEI校验失败提示（英文）- 参数：行号
     */
    public static final String XIAOMI_SN_VALIDATION_FAILED_EN = "Line {0}, the SN/IMEI is not a Xiaomi product!";
    public static final String XIAOMI_SN_VALIDATION_FAILED_EN_2 = "the SN/IMEI is not a Xiaomi product!";

    /**
     * 小米产品countryCode为空校验失败提示（中文）- 参数：行号
     */
    public static final String XIAOMI_COUNTRYCODE_EMPTY_FAILED_CN = "第{0}行国家编码校验失败！";
    public static final String XIAOMI_COUNTRYCODE_EMPTY_FAILED_CN_2 = "国家编码校验失败！国家编码为空！";
    /**
     * 小米产品countryCode为空校验失败提示（英文）- 参数：行号
     */
    public static final String XIAOMI_COUNTRYCODE_EMPTY_FAILED_EN = "Line {0}, Country Code is incorrect. Please enter the correct code, such as CN";
    public static final String XIAOMI_COUNTRYCODE_EMPTY_FAILED_EN_2 = "The verification of the countryCode failed! The countryCode is empty!";

    /**
     * retailerCode校验失败提示（中文）- 参数：行号
     */
    public static final String RETAILER_CODE_VALIDATION_FAILED_CN = "第{0}行零售商编码校验失败!";
    public static final String RETAILER_CODE_VALIDATION_FAILED_CN_2 = "零售商编码校验失败！零售商编码不能为空！";
    /**
     * retailerCode校验失败提示（英文）- 参数：行号
     */
    public static final String RETAILER_CODE_VALIDATION_FAILED_EN = "Line {0}, Retailer Code is incorrect. Please enter the correct " +
            "Retailer Code, such as RMSASI20250728067892";
    public static final String RETAILER_CODE_VALIDATION_FAILED_EN_2 = "The retailerCode validation failed! " +
            "retailerCode cannot be empty!";

    /**
     * SN为空提示（中文）- 参数：行号
     */
    public static final String SN_EMPTY_CN = "第{0}行SN/imei不能为空！";
    public static final String SN_EMPTY_CN_2 = "SN/imei不能为空！";
    /**
     * SN为空提示（英文）- 参数：行号
     */
    public static final String SN_EMPTY_EN = "Line {0}, SN/imei cannot be empty!";
    public static final String SN_EMPTY_EN_2 = "SN/imei cannot be empty!";

    /**
     * LDU类型错误提示（中文）- 参数：行号
     */
    public static final String LDU_TYPE_ERROR_CN = "第{0}行LDU类型错误!";
    public static final String LDU_TYPE_ERROR_CN_2 = "LDU类型错误!";
    /**
     * LDU类型错误提示（英文）- 参数：行号
     */
    public static final String LDU_TYPE_ERROR_EN = "Line {0}, LDU Type is incorrect. Please enter Mass Production Version or Customized Version";
    public static final String LDU_TYPE_ERROR_EN_2 = "the LDU type is error!";

    // ========================== 新增常量模板 ==========================
    /**
     * SN不在计划列表中无法停用提示（中文）- 参数：行号
     */
    public static final String SN_NOT_IN_PLAN_DEACTIVATE_FAILED_CN = "第{0}行，当前SN不在计划中，无法直接停用";
    public static final String SN_NOT_IN_PLAN_DEACTIVATE_FAILED_CN_2 = "已上报的的数据无法停用";
    /**
     * SN不在计划列表中无法停用提示（英文）- 参数：行号
     */
    public static final String SN_NOT_IN_PLAN_DEACTIVATE_FAILED_EN = "Line {0}, the SN doesn't exist in the LDU plan list, can not deactivate";
    public static final String SN_NOT_IN_PLAN_DEACTIVATE_FAILED_EN_2 = "Reported data cannot be disabled";

    /**
     * SN已停用重复操作提示（中文）- 参数：行号
     */
    public static final String SN_ALREADY_DEACTIVATED_CN = "第{0}行，当前已上报/已失效的的数据无法停用，请不要重复操作";
    /**
     * SN已停用重复操作提示（英文）- 参数：行号
     */
    public static final String SN_ALREADY_DEACTIVATED_EN = "Line {0}, Currently reported/invalid data cannot be disabled, please do not repeat the operation";

    /**
     * 文件上传大小超限提示（中文）- 参数：最大大小、当前大小
     */
    public static final String FILE_SIZE_EXCEED_CN = "文件大小超限！最大支持{0}MB，当前为{1}MB";
    /**
     * 文件上传大小超限提示（英文）- 参数：最大大小、当前大小
     */
    public static final String FILE_SIZE_EXCEED_EN = "File size exceeded! Maximum {0}MB, current {1}MB";

    /**
     * 目标数据已存在重复创建提示（中文）- 参数：行号
     */
    public static final String TARGET_DATA_EXISTS_CN = "第{0}行已有目标数据，则不允许再次创建";
    public static final String TARGET_DATA_EXISTS_CN_2 = "已有目标数据，则不允许再次创建";
    /**
     * 目标数据已存在重复创建提示（英文）- 参数：行号
     */
    public static final String TARGET_DATA_EXISTS_EN = "Line {0} is already target data, it is not allowed to be created again";
    public static final String TARGET_DATA_EXISTS_EN_2 = "Target data already exists, it is not allowed to be created again";

    /**
     * Sku为空提示（中文）- 参数：行号
     */
    public static final String SKU_EMPTY_CN = "第{0}行,sku不能为空！";
    public static final String SKU_EMPTY_CN_2 = "sku不能为空！";
    /**
     * Sku为空提示（英文）- 参数：行号
     */
    public static final String SKU_EMPTY_EN = "Line {0}, sku cannot be empty!";
    public static final String SKU_EMPTY_EN_2 = "sku cannot be empty!";


    public static final String TEMPLATE_ERROR = "请下载使用正确的模板（Please download and use the correct template)";
    // ========================== 通用工具方法 ==========================

    /**
     * 生成目标数据已存在重复创建的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getTargetDataExistsMessage(int lineNum) {
        return format(TARGET_DATA_EXISTS_CN, lineNum) + " " +
                format(TARGET_DATA_EXISTS_EN, lineNum);
    }


    public static String getTargetDataExistsMessageNoNum() {
        return format(TARGET_DATA_EXISTS_CN_2) + " " +
                format(TARGET_DATA_EXISTS_EN_2);
    }

    public static String getTargetDataExistsMessage() {
        return format(TARGET_DATA_EXISTS_CN) + " " + format(TARGET_DATA_EXISTS_EN);
    }

    /**
     * 生成SKU为空的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getSkuEmptyMessage(int lineNum) {
        return format(SKU_EMPTY_CN, lineNum) + " " +
                format(SKU_EMPTY_EN, lineNum);
    }

    public static String getSkuEmptyMessage() {
        return format(SKU_EMPTY_CN_2) + " " +
                format(SKU_EMPTY_EN_2);
    }

    /**
     * 根据模板和参数生成消息
     *
     * @param template 消息模板（含占位符{0},{1}等）
     * @param args     模板参数（数量需与占位符匹配）
     * @return 格式化后的消息字符串
     */
    public static String format(String template, Object... args) {
        // 验证参数合法性
        if (template == null || template.isEmpty()) {
            throw new IllegalArgumentException("消息模板不能为空");
        }
        return MessageFormat.format(template, args);
    }

    /**
     * 生成SN重复的双语提示消息
     * （针对高频使用场景提供专用方法，避免直接使用format时的参数错误）
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getSnDuplicateMessage(int lineNum) {
        return format(SN_DUPLICATE_CN, lineNum) + " " + format(SN_DUPLICATE_EN, lineNum);
    }

    public static String getSnDuplicateMessage() {
        return format(SN_DUPLICATE_CN_2) + " " + format(SN_DUPLICATE_EN_2);
    }

    /**
     * 生成小米产品SN/IMEI校验失败的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getXiaomiSnValidationFailedMessage(int lineNum) {
        return format(XIAOMI_SN_VALIDATION_FAILED_CN, lineNum) + " " +
                format(XIAOMI_SN_VALIDATION_FAILED_EN, lineNum);
    }

    public static String getXiaomiSnValidationFailedMessage() {
        return format(XIAOMI_SN_VALIDATION_FAILED_CN_2) + " " +
                format(XIAOMI_SN_VALIDATION_FAILED_EN_2);
    }

    public static String matchRetailerCodeAndCountry(int lineNum) {
        return format(RETAILER_AND_COUNTRY_NO_MATH_CN, lineNum) + " " +
                format(RETAILER_AND_COUNTRY_NO_MATH_EN, lineNum);
    }

    //请下载使用正确的模板（Please download and use the correct template
    public static String getTemplateError() {
        return TEMPLATE_ERROR;
    }


    public static String matchRetailerCodeAndCountry() {
        return format(RETAILER_AND_COUNTRY_NO_MATH_CN2) + " " +
                format(RETAILER_AND_COUNTRY_NO_MATH_EN2);
    }

    public static String productIdNotExistsMessage(int lineNum) {
        return format(PRODUCT_ID_NOT_EXIST_EN, lineNum);
    }

    public static String targetNumsNotExistsMessage(int lineNum) {
        return format(TARGET_NUMS_NOT_EXIST_CN, lineNum) + " " +
                format(TARGET_NUMS_NOT_EXIST_EN, lineNum);
    }

    public static String targetProductNumsNotExistsMessage(int lineNum) {
        return format(TARGET_PRODUCT_NUMS_NOT_EXIST_CN, lineNum) + " " +
                format(TARGET_PRODUCT_NUMS_NOT_EXIST_EN, lineNum);
    }


    /**
     * 生成产品countryCode为空校验失败的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getXiaomiCountryCodeEmptyMessage(int lineNum) {
        return format(XIAOMI_COUNTRYCODE_EMPTY_FAILED_CN, lineNum) + " " +
                format(XIAOMI_COUNTRYCODE_EMPTY_FAILED_EN, lineNum);
    }

    public static String getXiaomiCountryCodeEmptyMessage() {
        return format(XIAOMI_COUNTRYCODE_EMPTY_FAILED_CN_2) + " " +
                format(XIAOMI_COUNTRYCODE_EMPTY_FAILED_EN_2);
    }

    // 可根据需要添加更多专用消息生成方法，如文件大小超限提示等
    public static String getFileSizeExceedMessage(double maxSize, double currentSize) {
        return format(FILE_SIZE_EXCEED_CN, maxSize, currentSize) + " " +
                format(FILE_SIZE_EXCEED_EN, maxSize, currentSize);
    }

    /**
     * 生成LDU类型错误的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getLduTypeErrorMessage(int lineNum) {
        return format(LDU_TYPE_ERROR_CN, lineNum) + " " +
                format(LDU_TYPE_ERROR_EN, lineNum);
    }


    public static String getLduTypeErrorMessage() {
        return format(LDU_TYPE_ERROR_CN_2) + " " +
                format(LDU_TYPE_ERROR_EN_2);
    }

    /**
     * 生成SN不在计划中无法停用的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getSnNotInPlanDeactivateFailedMessage(int lineNum) {
        return format(SN_NOT_IN_PLAN_DEACTIVATE_FAILED_CN, lineNum) + " " +
                format(SN_NOT_IN_PLAN_DEACTIVATE_FAILED_EN, lineNum);
    }

    public static String getSnNotInPlanDeactivateFailedMessage() {
        return format(SN_NOT_IN_PLAN_DEACTIVATE_FAILED_CN_2) + " " +
                format(SN_NOT_IN_PLAN_DEACTIVATE_FAILED_EN_2);
    }

    public static String getSnAlreadyDeactivatedMessage(int lineNum) {
        return format(SN_ALREADY_DEACTIVATED_CN, lineNum) + " " +
                format(SN_ALREADY_DEACTIVATED_EN, lineNum);
    }

    /**
     * 生成retailerCode校验失败的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getRetailerCodeEmptyMessage(int lineNum) {
        return format(RETAILER_CODE_VALIDATION_FAILED_CN, lineNum) + " " +
                format(RETAILER_CODE_VALIDATION_FAILED_EN, lineNum);
    }

    public static String getRetailerCodeEmptyMessage() {
        return format(RETAILER_CODE_VALIDATION_FAILED_CN_2) + " " +
                format(RETAILER_CODE_VALIDATION_FAILED_EN_2);
    }

    /**
     * 生成SN为空的双语提示消息
     *
     * @param lineNum 行号
     * @return 双语拼接的提示信息
     */
    public static String getSnEmptyMessage(int lineNum) {
        return format(SN_EMPTY_CN, lineNum) + " " +
                format(SN_EMPTY_EN, lineNum);
    }

    public static String getSnEmptyMessage() {
        return format(SN_EMPTY_CN_2) + " " +
                format(SN_EMPTY_EN_2);
    }

}
