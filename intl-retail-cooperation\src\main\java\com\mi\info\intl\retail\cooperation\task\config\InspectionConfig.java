package com.mi.info.intl.retail.cooperation.task.config;

import com.alibaba.nacos.api.annotation.NacosProperties;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.mi.info.intl.retail.cooperation.task.app.mq.SRTaskActionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Configuration
@NacosPropertySource(dataId = "supervisor-inspection-config", autoRefreshed = true)
public class InspectionConfig {
    @NacosValue(value = "${inspection.task.countryWhiteList:SG}", autoRefreshed = true)
    private String countryWhiteListConfig;
    
    @NacosValue(value = "${inspection.task.supervisorJobIds:500900002,100000051,100000024}", autoRefreshed = true)
    private String supervisorJobIds;
    
    @NacosValue(value = "${inspection.task.mustDoEventDefinitionIds:1096,1097}", autoRefreshed = true)
    private String mustDoEventDefinitionIds;
    @NacosValue(value = "${inspection.task.anyOneEventDefinitionIds:1090,1091,1094,1095}", autoRefreshed = true)
    private String anyOneEventDefinitionIds;

    @NacosValue(value = "${inspection.task.midGrayList:}", autoRefreshed = true)
    private String midGrayList;
    
    List<Long> defaultMustDoEvent = Arrays.asList(SRTaskActionEnum.SALES_UPLOAD_QTY.getActionCode(),
            SRTaskActionEnum.STOCK_UPLOAD.getActionCode());
    
    List<Long> defaultAnyOneEvent = Arrays.asList(SRTaskActionEnum.SAMPLE_DEVICE_REPORTING_SR.getActionCode(),
            SRTaskActionEnum.DISPLAY_INSPECTION.getActionCode(), SRTaskActionEnum.IN_STORE_TRAINING.getActionCode(),
            SRTaskActionEnum.STORE_CHECK_SR.getActionCode());
    
    public List<String> getCountryWhiteList() {
        List<String> list = new ArrayList<>();
        if (countryWhiteListConfig != null && !countryWhiteListConfig.isEmpty()) {
            for (String s : countryWhiteListConfig.split(",")) {
                list.add(s.trim());
            }
        }
        return list;
    }
    
    public List<String> getSupervisorJobIdsList() {
        List<String> list = new ArrayList<>();
        if (supervisorJobIds != null && !supervisorJobIds.isEmpty()) {
            for (String s : supervisorJobIds.split(",")) {
                list.add(s.trim());
            }
        }
        return list;
    }
    
    public List<Long> getMustDoEventDefinitionIds() {
        List<Long> list = new ArrayList<>();
        if (mustDoEventDefinitionIds != null && !mustDoEventDefinitionIds.isEmpty()) {
            for (String s : mustDoEventDefinitionIds.split(",")) {
                list.add(Long.valueOf(s.trim()));
            }
        }
        return list;
    }
    
    public List<Long> getAnyOneEventDefinitionIds() {
        List<Long> list = new ArrayList<>();
        if (anyOneEventDefinitionIds != null && !anyOneEventDefinitionIds.isEmpty()) {
            for (String s : anyOneEventDefinitionIds.split(",")) {
                list.add(Long.valueOf(s.trim()));
            }
        }
        return list;
    }

    // mid 灰度名单
    public List<Long> getMidGrayList() {
        List<Long> list = new ArrayList<>();
        if (midGrayList != null && !midGrayList.isEmpty()) {
            for (String s : midGrayList.split(",")) {
                list.add(Long.valueOf(s.trim()));
            }
        }
        return list;
    }
}
