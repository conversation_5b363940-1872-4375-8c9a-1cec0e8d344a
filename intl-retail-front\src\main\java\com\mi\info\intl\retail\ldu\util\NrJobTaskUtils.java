package com.mi.info.intl.retail.ldu.util;

import cn.hutool.core.lang.ObjectId;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.NrJobGateway;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoOut;
import com.xiaomi.keycenter.po.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class NrJobTaskUtils {

    @Resource
    private NrJobGateway nrJobGateway;

    /**
     * 触发 NrJob 任务
     *
     * @param nrJobGoIn 任务请求参数
     * @return 任务执行结果
     */
    public <T> CommonResponse<String> triggerNrJobTask(NrJobGoIn nrJobGoIn) {
        CommonResponse<String> result = new CommonResponse<>(Result.SUCCESS_CODE);
           try {
            // 构建任务参数
            NrJobGoOut nrJobGoOut = nrJobGateway.triggerJob(nrJobGoIn);
            log.info("triggerNrJobTask-nrJobGoIn:{},goOut:{}", JSONUtil.toJsonStr(nrJobGoIn), JSONUtil.toJsonStr(nrJobGoOut));
            if (!"0".equals(nrJobGoOut.getCode())) {
                result.setMessage(nrJobGoOut.getMessage());
                result.setCode(Integer.parseInt(nrJobGoOut.getCode()));
                return result;
            }
            return result;
        } catch (Exception e) {
            log.error("触发NrJob任务失败", e);
            result.setCode(Integer.parseInt(ResultCodeEnum.SYSTEM_ERROR.getCode().toString()));
            return result;
        }
    }

}
