package com.mi.info.intl.retail.intlretail.service.api.mq.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RmsSyncDataRequest implements Serializable {

    private static final long serialVersionUID = -4235088978698575427L;

    private List<Serializable> dataList;
    /**
     * 取值：imei\qty
     */
    private String type;

    /**
     * 取值：
     * create
     * report verification
     * activate verification
     */
    private String operateType;

    private List<String> fields;
}
