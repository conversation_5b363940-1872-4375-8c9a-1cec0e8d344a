package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * QTY提交请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
public class SubmitQtyReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 匹配规则ID
     */
    private Integer ruleId;

    /**
     * 国家code，如 CN (中国)、US (美国)
     */
    private String countryCode;

    /**
     * 阵地code
     */
    private String positionCode;

    /**
     * 当前用户GUID
     */
    private String userId;

    /**
     * 当前用户虚拟miId
     */
    private Long miId;

    /**
     * QTY明细数组
     */
    private List<QtyDetailDto> detailList;

    /**
     * 图片数组
     */
    private List<QtyPhotoDto> photoList;

    /**
     * QTY明细DTO
     */
    @Data
    public static class QtyDetailDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 明细uuid
         */
        private String detailId;

        /**
         * 产品ID（五位数，非系统自增主键）
         */
        private Integer productId;

        /**
         * 销售数量
         */
        private String quantity;

        /**
         * 备注
         */
        private String note;

        /**
         * 销售时间，格式为 yyyy-MM-dd HH:mm:ss
         */
        private Long salesTime;
    }

    /**
     * QTY图片DTO
     */
    @Data
    public static class QtyPhotoDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 对应QTY明细uuid
         */
        private String detailId;

        /**
         * 照片的 URL 地址
         */
        private String url;

        /**
         * 上传时间，格式为 yyyy-MM-dd HH:mm:ss
         */
        private String uploadTime;
    }
} 