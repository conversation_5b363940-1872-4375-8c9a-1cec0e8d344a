package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
public class IntlLduReportReq extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 853257008547568237L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("displayStatus")
    private Integer displayStatus;

    @JsonProperty("lduType")
    private String lduType;

    @JsonProperty("mishowInstalled")
    private boolean mishowInstalled;

    @JsonProperty("planned")
    private boolean planned;

    @JsonProperty("productId")
    private String productId;

    @JsonProperty("productLine")
    private String productLine;

    @JsonProperty("productName")
    private String productName;

    @JsonProperty("projectCode")
    private String projectCode;

    @JsonProperty("countryCode")
    private List<String> countryCode;

    @JsonProperty("reportStartTime")
    private Long reportStartTime;

    @JsonProperty("reportEndTime")
    private Long reportEndTime;

    @JsonProperty("longReportStartTime")
    private Long longReportStartTime;

    @JsonProperty("longReportEndTime")
    private Long longReportEndTime;

    @JsonProperty("retailerCode")
    private String retailerCode;

    @JsonProperty("storeCode")
    private String storeCode;

    @JsonProperty("modificationReason")
    private Integer modificationReason;

    @JsonProperty("planStatus")
    private Integer planStatus;

    @JsonProperty("mishowStatus")
    private Integer mishowStatus;

    @JsonProperty("startDate")
    private String startDate;

    @JsonProperty("endDate")
    private String endDate;

    @JsonProperty("search")
    private String search;

    @JsonProperty("offset")
    private Long offset;

    @JsonProperty("userAccount")
    private String userAccount;

    @JsonProperty("sn")
    private String sn;

}
