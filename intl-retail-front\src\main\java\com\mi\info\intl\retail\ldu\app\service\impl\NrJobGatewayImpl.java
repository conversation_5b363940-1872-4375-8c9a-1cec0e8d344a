package com.mi.info.intl.retail.ldu.app.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.NrJobGateway;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoOut;
import com.mi.info.intl.retail.ldu.config.LduConfig;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/17
 */
@Slf4j
@Component
public class NrJobGatewayImpl implements NrJobGateway {

    @Reference(check = false, group = "${nr.dubbo.group:}", interfaceClass = NrJobService.class, timeout = 3000)
    private NrJobService nrJobService;

    @Resource
    private LduConfig lduConfig;


    @Override
    public NrJobGoOut triggerJob(NrJobGoIn nrJobGoIn) {
        TriggerJobRequestDTO triggerJobRequestDTO = Convert.convert(TriggerJobRequestDTO.class, nrJobGoIn);
        triggerJobRequestDTO.setProjectId(lduConfig.getId());
        triggerJobRequestDTO.setProjectName(lduConfig.getName());
        triggerJobRequestDTO.setOwner(nrJobGoIn.getOwner());
        log.info("triggerJobForTarget#triggerJob,req:{}", JSONUtil.toJsonStr(triggerJobRequestDTO));
        Result<String> result = nrJobService.triggerJob(triggerJobRequestDTO);
        log.info("triggerJobForTarget#triggerJob,resp:{}", JSONUtil.toJsonStr(result));
        NrJobGoOut nrJobGoOut = NrJobGoOut.builder().code(String.valueOf(result.getCode())).message("success").build();
        String status = result.getAttachments().get("status");
        if (StringUtils.isNotBlank(status)) {
            nrJobGoOut.setCode(status);
            nrJobGoOut.setMessage(result.getData());
        }
        return nrJobGoOut;
    }
}
