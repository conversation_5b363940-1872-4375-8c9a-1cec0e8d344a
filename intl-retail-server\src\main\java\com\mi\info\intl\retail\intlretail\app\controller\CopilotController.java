package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.copilot.ICopilotService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.nr.copilot.api.request.AIAssistantAnswerVoteRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantComparableProductRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantContrastProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantFeedbackTagsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.BreakAIAnswerRequest;
import com.xiaomi.nr.copilot.api.request.CopilotComparisonHistoryRequest;
import com.xiaomi.nr.copilot.api.request.CopilotConfigRequest;
import com.xiaomi.nr.copilot.api.request.CopilotTokenRequest;
import com.xiaomi.nr.copilot.api.request.CreateConversationRequest;
import com.xiaomi.nr.copilot.api.request.GetItemListRequest;
import com.xiaomi.nr.copilot.api.request.GetSpuInfoRequest;
import com.xiaomi.nr.copilot.api.response.AIAssistantAnswerVoteResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantComparableProductResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantContrastProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantFeedbackTagsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductComparisonHistoryResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.CopilotConfigResponse;
import com.xiaomi.nr.copilot.api.response.CreateConversationResponse;
import com.xiaomi.nr.copilot.api.response.GoodsItemResponseCate;
import com.xiaomi.nr.copilot.api.response.SpuInfoReponse;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping({"/api/proxy/copilot", "/*/api/proxy/copilot"})
public class CopilotController extends BaseController {

    @Resource
    private ICopilotService iCopilotService;

    private static final String DEFAULT_AREA_ID = "ID";
    private static final String DEFAULT_LANGUAGE = "en-US";

    /**
     * 渠道零售默认id=1
     */
    public static final Integer DEFAULT_CHANNEL_ID = 1;

    @PostMapping("/aiassistant/config")
    @ResponseBody
    public CommonApiResponse<CopilotConfigResponse> getConfig(@RequestBody CopilotConfigRequest request,
                                                              HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());

        CopilotConfigResponse configResponse;
        if (DEFAULT_AREA_ID.equals(request.getAreaId())) {
            // 印尼地区查询接口
            configResponse = iCopilotService.getConfig(request);
        } else {
            // 非印尼地区，直接返回0=不显示Copilot入口
            configResponse = new CopilotConfigResponse();
            configResponse.setChannelId(request.getChannelId());
            configResponse.setAreaId(request.getAreaId());
            configResponse.setUserId(request.getUserId());
            configResponse.setShowCopilot(0);
        }
        return new CommonApiResponse<>(configResponse);
    }

    @PostMapping("/aianswer/token")
    @ResponseBody
    public CommonApiResponse<String> getToken(@RequestBody CopilotTokenRequest request,
                                              HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        String token = iCopilotService.getToken(request);

        return new CommonApiResponse<>(token);
    }

    @PostMapping("/getItemList")
    @ResponseBody
    public CommonApiResponse<List<GoodsItemResponseCate>> getItemList(@RequestBody GetItemListRequest request,
                                                                      HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        List<GoodsItemResponseCate> records = iCopilotService.getItemList(request);
        return new CommonApiResponse<>(records);
    }

    @PostMapping("/chatAiAssistant/spuInfo")
    @ResponseBody
    public CommonApiResponse<SpuInfoReponse> getSpuInfo(@RequestBody GetSpuInfoRequest request,
                           HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }
        request.setAreaId(getAreaId(httpServletRequest));
        SpuInfoReponse response = iCopilotService.getSpuInfo(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/chatAiAssistant/breakAIAnswer")
    @ResponseBody
    public CommonApiResponse<Boolean> breakAIAnswer(@RequestBody BreakAIAnswerRequest request) {
        Boolean b = iCopilotService.breakAIAnswer(request);
        return new CommonApiResponse<>(b);
    }

    @PostMapping("/chatAiAssistant/getProductParams")
    @ResponseBody
    public CommonApiResponse<AIAssistantProductParamsResponse> getProductParams(@RequestBody AIAssistantProductParamsRequest request,
                                                                                HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }
        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        request.setLanguage(getLanguage(httpServletRequest));
        AIAssistantProductParamsResponse response = iCopilotService.getProductParams(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/chatAiAssistant/getComparableProducts")
    @ResponseBody
    public CommonApiResponse<AIAssistantComparableProductResponse> getComparableProducts(@RequestBody AIAssistantComparableProductRequest request,
                                                                                         HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }
        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        request.setLanguage(getLanguage(httpServletRequest));

        AIAssistantComparableProductResponse response = iCopilotService.getComparableProducts(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/chatAiAssistant/contrastProductParams")
    @ResponseBody
    public CommonApiResponse<AIAssistantContrastProductParamsResponse> contrastProductParams(@RequestBody AIAssistantContrastProductParamsRequest request,
                                                                                             HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }
        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        request.setLanguage(getLanguage(httpServletRequest));

        AIAssistantContrastProductParamsResponse response = iCopilotService.contrastProductParams(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/chatAiAssistant/getUserComparisonHistory")
    @ResponseBody
    public CommonApiResponse<AIAssistantProductComparisonHistoryResponse> getUserComparisonHistory(@RequestBody CopilotComparisonHistoryRequest request,
                                                                                                   HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        request.setLanguage(getLanguage(httpServletRequest));

        AIAssistantProductComparisonHistoryResponse response = iCopilotService.getUserComparisonHistory(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/chatAiAssistant/getFeedbackTags")
    @ResponseBody
    public CommonApiResponse<AIAssistantFeedbackTagsResponse> getFeedbackTags(@RequestBody AIAssistantFeedbackTagsRequest request,
                                                                              HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        request.setLanguage(getLanguage(httpServletRequest));

        AIAssistantFeedbackTagsResponse response = iCopilotService.getFeedbackTags(request);
        return new CommonApiResponse<>(response);
    }

    @PostMapping("/createConversation")
    @ResponseBody
    public CommonApiResponse<CreateConversationResponse> createConversation(@RequestBody CreateConversationRequest request,
                                                                            HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        try {
            CreateConversationResponse response = iCopilotService.createConversation(request);
            return new CommonApiResponse<>(response);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/answerVote")
    @ResponseBody
    public CommonApiResponse<AIAssistantAnswerVoteResponse> answerVote(@RequestBody AIAssistantAnswerVoteRequest request,
                                                                       HttpServletRequest httpServletRequest) {
        if (request.getChannelId() == null) {
            request.setChannelId(DEFAULT_CHANNEL_ID);
        }

        request.setAreaId(getAreaId(httpServletRequest));
        request.setUserId(getAccount());
        try {
            AIAssistantAnswerVoteResponse response = iCopilotService.answerVote(request);
            return new CommonApiResponse<>(response);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
    }

    private String getAreaId(HttpServletRequest request) {
        String areaId = request.getHeader("X-Retail-Global-Area");
        if (StringUtils.isNotBlank(areaId)) {
            return areaId;
        }

        // 兜底方案
        log.warn("no areaId found from http headers");
        return DEFAULT_AREA_ID;
    }

    private String getLanguage(HttpServletRequest request) {
        String language = request.getHeader("X-Retail-Language");
        if (StringUtils.isNotBlank(language)) {
            return language;
        }

        // 兜底方案
        log.warn("no language found from http headers");
        return DEFAULT_LANGUAGE;
    }
}
