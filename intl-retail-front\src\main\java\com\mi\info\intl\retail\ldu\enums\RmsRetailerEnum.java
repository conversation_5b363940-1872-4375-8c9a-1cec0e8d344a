package com.mi.info.intl.retail.ldu.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-07-24
 */
@Getter
public enum RmsRetailerEnum {

    NULL(0, ""),
    IR(10, "IR"),
    NKA(20, "NKA"),
    DKA(30, "DKA"),
    <PERSON><PERSON>(40, "OPR"),
    CES(50, "CES"),
    XIAOMI_STORE(60, "Xiaomi Store");

    private int code;
    private String name;

    RmsRetailerEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getEnumByCode(int code) {
        for (RmsRetailerEnum rmsRetailerEnum : RmsRetailerEnum.values()) {
            if (rmsRetailerEnum.getCode() == code) {
                return rmsRetailerEnum.name;
            }
        }
        return "";
    }


}
