package com.mi.info.intl.retail.intlretail.service.api.inspection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.request.*;
import com.mi.info.intl.retail.intlretail.service.api.result.InspectionTaskConfDTO;
import com.mi.info.intl.retail.intlretail.service.api.result.PersonAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.intlretail.service.api.result.SupervisorTaskListResponse;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @date 2025/6/12
 **/
public interface InspectionService {

    CommonApiResponse<IPage<InspectionTaskConfDTO>> pageList(IntlInspectionTaskRequest request);

    CommonApiResponse<String> export(IntlInspectionTaskRequest request);

    CommonApiResponse<String> stopTask(StopInspectionTaskRequest request);

    CommonResponse<SupervisorTaskListResponse> getBigPromotionTaskList(BigPromotionConfRequest request);

    CommonResponse<String> bigPromotionCreate(BigPromotionConfCreateRequest request);

    CommonResponse<String> bigPromotionStop(BigPromotionConfStopRequest request);

    CommonResponse<String>  bigPromotionStopExportTask(BigPromotionConfRequest request);

    /**
     * 根据miId列表获取国家和区域信息
     *
     * @param request miId列表参数
     * @return 国家和区域信息
     */
    CommonApiResponse<PersonAreaResponse> getPersonAreaInfo(PersonAreaRequest request);
}
