# IMEI查询接口实现文档

## 概述

根据接口详细设计，实现了3个IMEI查询接口：
1. IMEI明细列表查询接口
2. IMEI明细页查询接口  
3. IMEI汇总数据查询接口

## 接口列表

### 1. IMEI明细列表查询接口

**接口地址**: `POST /api/so/queryImeiListByPage`

**功能**: 分页查询IMEI明细列表，支持多种筛选条件和时间范围查询

**主要特性**:
- 支持促销员和督导两种角色的数据权限控制
- 支持模糊搜索（IMEI/SN/产品名称）
- 支持多种筛选条件（门店、产品线、渠道类型等）
- 支持年月筛选和自定义时间段筛选
- 返回分页数据和日期汇总统计

### 2. IMEI明细页查询接口

**接口地址**: `POST /api/so/queryImeiDetail`

**功能**: 根据IMEI ID查询单条IMEI明细信息

**主要特性**:
- 返回完整的IMEI明细信息
- 包含产品信息、门店信息、校验结果等
- 显示创建用户信息

### 3. IMEI汇总数据查询接口

**接口地址**: `POST /api/so/queryImeiSummary`

**功能**: 查询IMEI数据的汇总统计信息

**主要特性**:
- 统计总数量、成功数量、校验中数量、失败数量
- 支持与明细查询相同的筛选条件
- 按校验结果分组统计

## 实现架构

### 代码结构

```
intl-retail-api/
├── dto/
│   ├── ImeiListQueryReq.java          # 明细列表查询请求
│   ├── ImeiListQueryResp.java         # 明细列表查询响应
│   ├── ImeiDetailQueryReq.java        # 明细查询请求
│   ├── ImeiDetailQueryResp.java       # 明细查询响应
│   ├── ImeiSummaryQueryReq.java       # 汇总查询请求
│   └── ImeiSummaryQueryResp.java      # 汇总查询响应

intl-retail-sales/
├── ImeiUploadServiceImpl.java         # 服务实现类
├── IntlSoImeiMapper.java             # 数据访问层
└── IntlSoImeiMapper.xml              # SQL映射文件

intl-retail-server/
└── SoController.java                 # 控制器层
```

### 核心实现

#### 1. 权限控制
- 促销员（userTitle in [500900001, 100000027, 100000026]）：只能查看自己上报的数据
- 督导及其他角色：可以查看关联门店的数据，通过内部API查询用户关联的门店列表

#### 2. 时区转换
- 使用 `IntlTimeUtil` 进行时区转换
- 支持年月筛选和自定义时间段筛选
- 将用户输入的时间转换为对应国家的本地时间戳

#### 3. 数据库查询
- 使用MyBatis进行多表关联查询
- 涉及表：`intl_so_imei`、`intl_so_org_info`、`intl_so_user_info`、`intl_rms_product`、`intl_rms_store`、`intl_rms_user`
- 支持动态条件查询和分页

#### 4. 内部API调用
- 通过 `IGateWayChannelInfoService` 查询用户关联的门店信息
- 使用 `BusinessDataInputRequest` 构建查询请求

## 数据库设计

### 主要表结构

1. **intl_so_imei**: IMEI主表
   - 存储IMEI/SN信息（明文和掩码）
   - 关联产品、组织、用户信息
   - 包含校验结果和销售时间

2. **intl_so_org_info**: 组织信息表
   - 存储门店和阵地信息
   - 关联国家代码

3. **intl_so_user_info**: 用户信息表
   - 存储销售人员和创建人信息
   - 关联职位信息

### SQL查询特点

- 使用LEFT JOIN进行多表关联
- 支持动态WHERE条件
- 使用LIMIT进行分页
- 使用GROUP BY进行统计汇总

## 测试

### 单元测试
- 创建了 `ImeiQueryControllerTest` 测试类
- 覆盖正常流程和异常情况
- 使用Mock进行服务层测试

### 测试用例
1. 成功查询测试
2. 参数验证测试
3. 权限控制测试
4. 分页功能测试

## 部署说明

### 依赖要求
- Spring Boot 2.x
- MyBatis Plus
- 内部API服务（IGateWayChannelInfoService）

### 配置要求
- 数据库连接配置
- 内部API服务配置
- 时区配置

## 注意事项

1. **性能优化**
   - 大数据量查询时建议添加索引
   - 考虑使用缓存优化频繁查询

2. **安全考虑**
   - IMEI/SN数据已进行掩码处理
   - 实现了基于角色的数据权限控制

3. **扩展性**
   - 预留了扩展字段
   - 支持新增筛选条件

4. **错误处理**
   - 统一的异常处理机制
   - 详细的错误日志记录

## 后续优化建议

1. 添加缓存机制提升查询性能
2. 实现更细粒度的权限控制
3. 添加数据导出功能
4. 优化大数据量查询的性能
5. 完善时区转换的实现细节
