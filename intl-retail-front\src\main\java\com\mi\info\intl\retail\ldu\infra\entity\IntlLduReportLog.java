package com.mi.info.intl.retail.ldu.infra.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@TableName(value = "intl_ldu_report_log", autoResultMap = true)
@Data
public class IntlLduReportLog implements Serializable {

    private static final long serialVersionUID = -765432109876543210L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 上报ID
     */
    @TableField("report_id")
    private Long reportId;

    /**
     * 区域
     */
    @TableField("region")
    private String region;

    /**
     * 区域编码
     */
    @TableField("region_code")
    private String regionCode;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 国家/地区编码
     */
    @TableField("country_code")
    private String countryCode;


    /**
     * 渠道
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 业务大区
     */
    @TableField("biz_region")
    private String bizRegion;

    /**
     * 运营城市
     */
    @TableField("ops_city")
    private String opsCity;

    /**
     * 网格名称
     */
    @TableField("grid")
    private String grid;

    /**
     * 零售商编码
     */
    @TableField("retailer_code")
    private String retailerCode;

    /**
     * 零售商名称
     */
    @TableField("retailer_name")
    private String retailerName;

    /**
     * 门店编码
     */
    @TableField("store_code")
    private String storeCode;

    /**
     * 门店名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("district")
    private String district;

    /**
     * 产品线
     */
    @TableField("product_line")
    private String productLine;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 项目代码
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * RAM容量
     */
    @TableField("ram_capacity")
    private String ramCapacity;

    /**
     * ROM容量
     */
    @TableField("rom_capacity")
    private String romCapacity;

    /**
     * 序列号SN
     */
    @TableField("sn")
    private String sn;

    /**
     * 69Code
     */
    @TableField("69code")
    private String code69;

    /**
     * IMEI 1
     */
    @TableField("imei_1")
    private String imei1;

    /**
     * IMEI 2
     */
    @TableField("imei_2")
    private String imei2;

    /**
     * 在计划内：带值为Yes不在：带值为No
     */
    @TableField("plan_status")
    private Integer planStatus;

    /**
     * LDU类型:大货（Mass Production Version）、专样（Customized Version）
     */
    @TableField("ldu_type")
    private String lduType;

    /**
     * 有安装：带值为Yes 未安装：带值为No
     */
    @TableField("mishow_status")
    private Integer mishowStatus;

    /**
     * 最后一次成功获取Mishow信息的时间，精确到毫秒
     */
    @TableField("last_mishow_fetch_time")
    private Long lastMishowFetchTime;

    /**
     * 展陈中：上报后的默认状态 已丢失：LDU上报数据修改为丢失后 已损坏：LDU上报数据修改为损坏后 已更换：LDU上报数据修改为更换后
     */
    @TableField("display_status")
    private Integer displayStatus;

    /**
     * 上报距离差
     */
    @TableField("report_distance")
    private BigDecimal reportDistance;

    /**
     * 创建人ID/上报人ID
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建人姓名/上报人姓名
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 创建时间/上报时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新人ID
     */
    @TableField("update_user_id")
    private String updateUserId;

    /**
     * 更新名字
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 上报角色
     */
    @TableField(value = "report_role")
    private String reportRole;

    /**
     * 数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 颜色
     */
    @TableField(value = "color")
    private String color;


    /**
     * 文件信息
     */
    @TableField(exist = false)
    private List<IntlFileUpload> fileUploadList;

}
