package com.mi.info.intl.retail.so.app;

import cn.hutool.core.collection.CollUtil;
import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncSoDataService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsSyncDataRequest;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.xiaomi.cnzone.commons.exception.CommonBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = RmsSyncSoDataService.class)
public class RmsSyncSoDataServiceImpl implements RmsSyncSoDataService {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Value("${intl-retail.rocketmq.so-sync.topic}")
    private String topic;

    @Value("${intl-retail.rocketmq.so-sync.batch-size:100}")
    private Integer batchSize;

    @Value("${intl-retail.rocketmq.so-sync.max-size:500}")
    private Integer maxSize;

    @Override
    public CommonResponse syncRmsSoData(RmsSyncDataRequest request) {
        List<? extends Serializable> dataList = request.getDataList();
        if (CollUtil.isEmpty(dataList) || dataList.size() > maxSize) {
            log.error("dataList 异常，size={}", dataList == null ? "null" : dataList.size());
            throw new CommonBusinessException("dataList异常");
        }

        List<RmsSyncSoDataReqDto> convertedList = convertDataList(dataList, request);
        List<List<RmsSyncSoDataReqDto>> split = CollUtil.split(convertedList, batchSize);

        split.forEach(batch -> {
            rocketMQTemplate.asyncSend(topic, batch, new RmsSendCallback(topic, batch));
            log.info("准备发送 syncRmsSoData 数据，共 {} 条", batch.size());
        });

        return new CommonResponse(200, "success", null);
    }

    private List<RmsSyncSoDataReqDto> convertDataList(List<? extends Serializable> dataList, RmsSyncDataRequest request) {
        List<RmsSyncSoDataReqDto> result = new ArrayList<>();
        dataList.forEach(e -> {
            try {
                RmsSyncSoDataReqDto item = new RmsSyncSoDataReqDto();
                item.setData(e);
                item.setType(request.getType());
                item.setOperateType(request.getOperateType());
                item.setFields(request.getFields());
                result.add(item);
            } catch (Exception ex) {
                log.error("convertDataList 转换失败，原始数据: {}，异常信息：{}", e, ex.getMessage());
                throw new CommonBusinessException("数据转换异常");
            }
        });
        return result;
    }

    private class RmsSendCallback implements SendCallback {
        private int retryCount = 0;
        private final int maxRetry = 3;
        private final String topic;
        private final Object messages;

        public RmsSendCallback(String topic, Object messages) {
            this.topic = topic;
            this.messages = messages;
        }

        @Override
        public void onSuccess(SendResult sendResult) {
            log.info("消息发送成功，topic={}, result={}", topic, sendResult);
        }

        @Override
        public void onException(Throwable e) {
            if (retryCount++ < maxRetry) {
                log.warn("消息发送失败，第 {} 次重试，topic={}", retryCount, topic, e);
                try {
                    Thread.sleep(1000L * retryCount);
                } catch (InterruptedException interruptedException) {
                    Thread.currentThread().interrupt();
                    log.warn("发送重试被中断", interruptedException);
                    return;
                }
                rocketMQTemplate.asyncSend(topic, messages, this);
            } else {
                log.error("消息发送最终失败，已重试{}次，topic={}", maxRetry, topic, e);
                // 可以存入数据库或死信队列进行后续处理
            }
        }
    }
}
