package com.mi.info.intl.retail.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mi.info.intl.retail.model.UserInfo;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class BaseReq implements Serializable {

    /**
     * 页码
     */
    @ApiDocClassDefine(value = "页码")
    private Integer pageNum;
    /**
     * 每页大小
     */
    @ApiDocClassDefine(value = "每页大小")
    private Integer pageSize;

    @JsonIgnore
    private UserInfo userInfo;

}
