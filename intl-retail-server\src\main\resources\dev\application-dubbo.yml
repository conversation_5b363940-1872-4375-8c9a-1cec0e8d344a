nacos:
  namespace: public
  address: nacos://sgp.nacos.test.b2c.srv:80
  config:
    address: sgp.nacos.test.b2c.srv:80
init:
  group: ${user.name}
  app:
    name: intl-retail

#dubbo provider
store:
  dubbo:
    group: ${user.name}
cache:
  dubbo:
    group: ${user.name}
push:
  dubbo:
    group: ${user.name}
center:
  dubbo:
    group: ${user.name}

#dubbo consumer
iib:
  dubbo:
    group: staging
maindata:
  dubbo:
    group: staging
copilot:
  dubbo:
    group: staging
proretailbi:
  dubbo:
    group: sg_staging
college:
  dubbo:
    group: sg_staging
eiam:
  dubbo:
    group: sg_staging

# Added from application-conf.yml
env: dev
miwork:
  alarm:
    groupId:
      p0: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p1: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p2: oc_1e6d1e54ac5eae84730fe55e86a013e7
nr:
  dubbo:
    group: sg_staging

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: 总部策略运营
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: 国家零售经理
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: 总部策略管理
      manageChannelList:
        - 27
  dubbo-group: sg_staging
