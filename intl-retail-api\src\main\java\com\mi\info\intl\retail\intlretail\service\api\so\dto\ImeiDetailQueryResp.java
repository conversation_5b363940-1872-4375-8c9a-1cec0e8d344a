package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IMEI明细页查询响应参数
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@Data
public class ImeiDetailQueryResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 产品名称
     */
    @JsonProperty("productName")
    private String productName;

    /**
     * IMEI1
     */
    @JsonProperty("imei1Mask")
    private String imei1Mask;

    /**
     * IMEI2
     */
    @JsonProperty("imei2Mask")
    private String imei2Mask;

    /**
     * SN
     */
    @JsonProperty("snMask")
    private String snMask;

    /**
     * 注释
     */
    @JsonProperty("note")
    private String note;

    /**
     * 门店名称
     */
    @JsonProperty("storeName")
    private String storeName;

    /**
     * IMEI校验结果code
     */
    @JsonProperty("verifyResult")
    private Integer verifyResult;

    /**
     * IMEI校验结果文本
     */
    @JsonProperty("verifyResultDetail")
    private String verifyResultDetail;

    /**
     * 创建用户
     */
    @JsonProperty("createdBy")
    private String createdBy;
}
