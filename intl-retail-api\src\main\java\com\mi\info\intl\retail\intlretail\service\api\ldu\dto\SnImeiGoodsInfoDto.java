package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 通过sn/imei查询的商品信息
 *
 * <AUTHOR>
 * @date 2025/7/8 20:06
 */
@Data
public class SnImeiGoodsInfoDto implements Serializable {

    private static final long serialVersionUID = 853257008547568237L;

    /**
     * 串号
     */
    @ApiDocClassDefine(value = "SN串号")
    @JsonProperty("sn")
    private String sn;

    /**
     * IMEI号
     */
    @ApiDocClassDefine(value = "IMEI号")
    @JsonProperty("imei")
    private String imei;

    /**
     * 第二IMEI号
     */
    @ApiDocClassDefine(value = "第二IMEI号")
    @JsonProperty("imei2")
    private String imei2;

    /**
     * 69码
     */
    @ApiDocClassDefine(value = "69码")
    @JsonProperty("code69")
    private String code69;

    /**
     * SKU
     */
    @ApiDocClassDefine(value = "SKU")
    @JsonProperty("sku")
    private String sku;

    /**
     * 商品ID
     */
    @ApiDocClassDefine(value = "商品ID")
    @JsonProperty("goodsId")
    private String goodsId;

    /**
     * 商品类型
     */
    @ApiDocClassDefine(value = "商品类型")
    @JsonProperty("goodsType")
    private Integer goodsType;

    /**
     * 商品名称
     */
    @ApiDocClassDefine(value = "商品名称")
    @JsonProperty("goodsName")
    private String goodsName;

    /**
     * 商品名称(英文)
     */
    @ApiDocClassDefine(value = "商品名称(英文)")
    @JsonProperty("goodsNameEn")
    private String goodsNameEn;

    /**
     * 供应链商品名称
     */
    @ApiDocClassDefine(value = "供应链商品名称")
    @JsonProperty("scmName")
    private String scmName;


    /**
     * 产品线 ID
     */
    @ApiDocClassDefine(value = "产品线")
    @JsonProperty("productLine")
    private String productLine;

    /**
     * 产品线中文
     */
    @ApiDocClassDefine(value = "产品线(中文)")
    @JsonProperty("productLineCn")
    private String productLineCn;

    /**
     * 产品线英文
     */
    @ApiDocClassDefine(value = "产品线(英文)")
    @JsonProperty("productLineEn")
    private String productLineEn;

    /**
     * 产品ID
     */
    @ApiDocClassDefine(value = "产品ID")
    @JsonProperty("productId")
    private String productId;

    /**
     *  产品名称
     */
    @ApiDocClassDefine(value = "产品名称")
    @JsonProperty("productName")
    private String productName;

    /**
     * 项目编码
     */
    @ApiDocClassDefine(value = "项目编码")
    @JsonProperty("projectCode")
    private String projectCode;

    /**
     * ram容量
     */
    @ApiDocClassDefine(value = "RAM容量")
    @JsonProperty("ram")
    private String ram;

    /**
     * rom容量
     */
    @ApiDocClassDefine(value = "ROM容量")
    @JsonProperty("rom")
    private String rom;

    /**
     * 编码类型
     */
    @ApiDocClassDefine(value = "编码类型")
    @JsonProperty("serialNumberType")
    private String serialNumberType;

    /**
     * 中文颜色
     */
    @ApiDocClassDefine(value = "颜色")
    @JsonProperty("color")
    private String color;

    /**
     * 英文颜色
     */
    @ApiDocClassDefine(value = "颜色(英文)")
    @JsonProperty("englishColor")
    private String englishColor;

}
