package com.mi.info.intl.retail.ldu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.ObjectId;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.BufferedImageLuminanceSource;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.LuminanceSource;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.google.zxing.common.HybridBinarizer;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduReportLogService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSnService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BarcodeScanReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.FileUploadReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlFileUploadDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportLogDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportSubmitReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.RmsPositionReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ScanCodeReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.StroeInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.ldu.config.LduConfig;
import com.mi.info.intl.retail.ldu.dto.IntlLduReportExport;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.mi.info.intl.retail.ldu.dto.StoreInfoDTO;
import com.mi.info.intl.retail.ldu.enums.DisplayStatusEnum;
import com.mi.info.intl.retail.ldu.enums.ExportExcelEnum;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.enums.LduTypeEnum;
import com.mi.info.intl.retail.ldu.enums.ReportStatusEnum;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.ldu.enums.SubmitType;
import com.mi.info.intl.retail.ldu.enums.ValidityStatusEnum;
import com.mi.info.intl.retail.ldu.enums.YesOrNoEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.imageio.ImageIO;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = IntlLduReportLogService.class)
public class IntlLduReportLogServiceImpl extends ServiceImpl<IntlLduReportLogMapper, IntlLduReportLog> implements IntlLduReportLogService {

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlLduReportLogMapper statisticsMapper;

    @Resource
    private NrJobTaskUtils nrJobTaskUtils;

    @Resource
    private IntlLduSnImeiService intlLduSnImeiService;

    @Resource
    private IntlLduSnService intlLduSnService;

    @Resource
    private IntlRmsPositionMapper intlRmsPositionMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Resource
    private IntlFileUploadMapper intlFileUploadMapper;

    @Resource
    private IntlLduTargetMapper intlLduTargetMapper;

    @Resource
    private LduConfig lduConfig;

    /**
     * 列表
     *
     * @param query
     * @return
     */
    @Override
    public CommonResponse<Page<IntlLduReportLogDto>> pageList(IntlLduReportReq query) {

        query.setOffset((query.getPageNum() - 1) * query.getPageSize());
        List<IntlLduReportLog> intlLduReportLogs = statisticsMapper.pageList(query);
        int count = statisticsMapper.pageListCount(query);

        return new CommonResponse<>(getIntlLduReportLogDtoPage(query, intlLduReportLogs, count));
    }

    @Override
    public CommonApiResponse<Page<IntlLduReportLogDto>> historyList(IntlLduReportReq query) {

        query.setOffset((query.getPageNum() - 1) * query.getPageSize());

        List<IntlLduReportLog> intlLduReportLogs = statisticsMapper.historyList(query);
        int count = statisticsMapper.historyListCount(query);

        return new CommonApiResponse<>(getIntlLduReportLogDtoPage(query, intlLduReportLogs, count));
    }

    private static Page<IntlLduReportLogDto> getIntlLduReportLogDtoPage(IntlLduReportReq query, List<IntlLduReportLog> intlLduReportLogs, int count) {
        Page<IntlLduReportLogDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(intlLduReportLogs)) {
            pageDTO.setCurrent(query.getPageNum());
            pageDTO.setSize(query.getPageSize());
            return pageDTO;
        }
        List<IntlLduReportLogDto> list = ComponentLocator.getConverter().convertList(intlLduReportLogs, IntlLduReportLogDto.class);
        list.forEach(dto -> {
            dto.setDisplayStatusName(DisplayStatusEnum.fromCode(dto.getDisplayStatus()).getZhDesc());
            dto.setPlanStatusName(YesOrNoEnum.getByCode(dto.getPlanStatus()).getDesc());
            dto.setMishowStatusName(YesOrNoEnum.getByCode(dto.getMishowStatus()).getDesc());
            List<IntlFileUploadDto> fileUploadList = dto.getFileUploadList();
            if (CollUtil.isNotEmpty(fileUploadList)) {
                dto.setImageUrlList(fileUploadList.stream()
                        .map(IntlFileUploadDto::getFdsUrl)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }

        });
        pageDTO.setTotal(count);
        pageDTO.setRecords(list);
        return pageDTO;
    }

    /**
     * 详情
     *
     * @param query
     * @return
     */
    @Override
    public CommonResponse<IntlLduReportLogDto> detail(IntlLduReportReq query) {
        Long id = query.getId();
        if (null == id) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "id cannot be empty");
        }

        QueryWrapper<IntlLduReportLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        IntlLduReportLog intlLduReportLog = this.getOne(queryWrapper);
        if (null == intlLduReportLog) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "The corresponding data does not exist.");
        }
        List<IntlFileUpload> intlFileUploads = intlFileUploadMapper
                .selectByBusyIdAndMoudle(intlLduReportLog.getReportId(), FileUploadEnum.LDU_UPLOAD.getCode());
        IntlLduReportLogDto dto = ComponentLocator.getConverter().convert(intlLduReportLog, IntlLduReportLogDto.class);
        if (CollUtil.isNotEmpty(intlFileUploads)) {
            List<IntlFileUploadDto> convertList = ComponentLocator.getConverter()
                    .convertList(intlFileUploads, IntlFileUploadDto.class);
            dto.setFileUploadList(convertList);
        }
        return new CommonResponse<>(dto);
    }

    /**
     * 修改
     *
     * @param query
     * @return
     */
    @Override
    public CommonResponse<String> update(IntlLduReportReq query) {
        Long id = query.getId();
        Integer modificationReason = query.getModificationReason();
        if (null == id) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "id id cannot be empty");
        }

        if (null == modificationReason) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Modification reason cannot be empty");
        }
        IntlLduReportLog intlLduReportLog = new IntlLduReportLog();
        intlLduReportLog.setId(id);
        intlLduReportLog.setDisplayStatus(modificationReason);
        this.updateById(intlLduReportLog);

        return new CommonResponse<>("success");
    }

    @Override
    public CommonResponse<String> excelExport(IntlLduReportReq query) {

        String upcAccount = RpcContext.getContext().getAttachment("$upc_account");

        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(ExportExcelEnum.LDU_REPORT.getExcelName(), ".xlsx");

            excelWriter = EasyExcel.write(tempFile, IntlLduReportExport.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(ExportExcelEnum.LDU_REPORT.getExcelName()).build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                CommonResponse<Page<IntlLduReportLogDto>> pageCommonResponse = this.pageList(query);

                List<IntlLduReportLogDto> records = pageCommonResponse.getData().getRecords();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }

                List<IntlLduReportExport> intlLduReportExports = ComponentLocator.getConverter().convertList(records, IntlLduReportExport.class);
                excelWriter.write(intlLduReportExports, writeSheet);

                hasNext = currentPage * pageSize < pageCommonResponse.getData().getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }

            if (currentPage == 1L) {
                return CommonResponse.failure(ResultCodeEnum.DATA_MISS.getCode(), ResultCodeEnum.DATA_MISS.getEnMsg());
            }

            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

            FdsUploadResult upload = fdsService.upload(ExportExcelEnum.LDU_REPORT
                    .getExcelEnName() + timestamp + ".xlsx", tempFile, true);

            return nrJobForExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("LDU Reporting Statistics List Export Exception: {}", e);
            return CommonResponse.failure(ResultCodeEnum.SYSTEM_ERROR.getCode(), ResultCodeEnum.SYSTEM_ERROR.getEnMsg());
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private CommonResponse<String> nrJobForExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.LDU_REPORT.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.LDU_REPORT.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.LDU_REPORT.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    @Override
    public CommonResponse<SnImeiGoodsInfoDto> scan(ScanCodeReq scanCodeReq) {
        log.info("scan-scanCodeReq: {}", JSONUtil.toJsonStr(scanCodeReq));
        String serialNumber = scanCodeReq.getSerialNumber();
        if (CharSequenceUtil.isBlank(serialNumber)) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "SN/IMEI/69 code cannot be empty");
        }
        SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(scanCodeReq.getSerialNumber());

        if (serialNumberType == SerialNumberType.UNKNOWN) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Serial code is not a valid SN/IMEI/69 code");
        }
        SnImeiGoodsInfoDto goodsInfoDto = intlLduSnImeiService.getGoodsInfoBySnImei(scanCodeReq.getSerialNumber());
        if (goodsInfoDto == null) {
            goodsInfoDto = new SnImeiGoodsInfoDto();
            return new CommonResponse<>(ResultCodeEnum.DATA_NOT_FOUND.getCode(), ResultCodeEnum.DATA_NOT_FOUND.getZhMsg(), goodsInfoDto);
        }
        String sn = goodsInfoDto.getSn();
        String imei = goodsInfoDto.getImei();
        String imei2 = goodsInfoDto.getImei2();
        String code69 = goodsInfoDto.getCode69();

        if (serialNumber.equals(sn)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.SN.toString());
        } else if (serialNumber.equals(imei)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.IMEI.toString());
        } else if (serialNumber.equals(imei2)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.IMEI.toString());
        } else if (serialNumber.equals(code69)) {
            goodsInfoDto.setSerialNumberType(SerialNumberType.CODE69.toString());
        } else {
            goodsInfoDto.setSerialNumberType(SerialNumberType.UNKNOWN.toString());
        }

        QueryWrapper<IntlLduReportLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", sn);
        IntlLduReportLog intlLduReportLog = this.getOne(queryWrapper);

        if (null != intlLduReportLog) {
            return new CommonResponse<>(ResultCodeEnum.REPEAT_OPERATION.getCode(), ResultCodeEnum.REPEAT_OPERATION.getEnMsg(), null);
        }

        log.info("scan-goodsInfoDto: {}", goodsInfoDto);
        return new CommonResponse<>(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getZhMsg(), goodsInfoDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Integer> submit(IntlLduReportSubmitReq intlLduReportDataReq, RmsUserBaseDataResponse userBaseInfo) {

        List<IntlFileUpload> intlFileUploads = new ArrayList<>();

        Integer submitType = intlLduReportDataReq.getSubmitType();
        if (null == submitType) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Submission type cannot be empty");
        }

        List<SnImeiGoodsInfoReq> goodsList = intlLduReportDataReq.getGoodsList();

        if (goodsList == null || goodsList.isEmpty()) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Product list cannot be empty");
        }

        // 离线上传的情况
        if (submitType.equals(SubmitType.OFFLINE_SUBMIT.getCode())) {
            offlineeUpload(goodsList, intlFileUploads);
            intlFileUploadMapper.batchByGuidFileUpload(intlFileUploads);
            return new CommonResponse<>(ResultCodeEnum.SUCCESS.getCode());
        }


        String countryCode = intlLduReportDataReq.getCountryCode();
        if (StringUtils.isBlank(countryCode)) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Country code cannot be empty");
        }

        String positionCode = intlLduReportDataReq.getPositionCode();
        if (StringUtils.isBlank(positionCode)) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Sales point information does not exist");
        }

        StoreInfoDTO intlRmsStore = intlRmsStoreMapper.getStoreByPositionCode(positionCode);


        IntlRmsCountryTimezone intlRmsCountryTimezone = intlRmsCountryTimezoneMapper.selectByCountryCode(countryCode);


        if (null == intlRmsCountryTimezone) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Country code does not have a corresponding region name");
        }

        // 上报对已经做了计划的SN修改为已上报
        List<IntlLduSnDto> intlLduSns = intlLduSnService.selectBySnAndCountryCode(goodsList, countryCode);
        List<String> finalSnList = updateLduSn(countryCode, intlLduSns);
        Map<String, IntlLduSnDto> snMap = new HashMap<>(intlLduSns.size());
        if (CollUtil.isNotEmpty(intlLduSns)) {
            snMap = intlLduSns.stream()
                    .collect(Collectors.toMap(
                            IntlLduSnDto::getSn,
                            sn -> sn
                    ));
        }


        List<IntlLduSnDto> intlLduSnList = new ArrayList<>();
        List<IntlLduReportLog> intlLduReportLogs = new ArrayList<>();
        for (SnImeiGoodsInfoReq goodsInfoDto : goodsList) {

            IntlLduReportLog statistics = createtIntlLduReportLog(intlLduReportDataReq, userBaseInfo,
                    goodsInfoDto, intlRmsCountryTimezone, countryCode, intlRmsStore);


            createOnlineUpload(goodsInfoDto, statistics, intlFileUploads, userBaseInfo);

            if (finalSnList == null) {
                finalSnList = Collections.emptyList();
            }

            createLduSnData(userBaseInfo, goodsInfoDto, finalSnList, statistics, intlLduSnList, snMap);
            intlLduReportLogs.add(statistics);

        }

        if (CollUtil.isNotEmpty(intlLduReportLogs)) {
            statisticsMapper.batchInsertReportLogs(intlLduReportLogs);
            statisticReportLogs(intlLduReportLogs, userBaseInfo);
        }

        if (CollUtil.isNotEmpty(intlLduSnList)) {
            intlLduSnService.batchInsert(intlLduSnList);
        }

        if (CollUtil.isNotEmpty(intlFileUploads)) {
            intlFileUploadMapper.batchInsertDatas(intlFileUploads);
        }

        return new CommonResponse<>(ResultCodeEnum.SUCCESS.getCode());
    }

    private void statisticReportLogs(List<IntlLduReportLog> intlLduReportLogs, RmsUserBaseDataResponse userBaseInfo) {
        List<IntlLduTarget> intlLduTargetList = new ArrayList<>();
        for (IntlLduReportLog statistics : intlLduReportLogs) {
            IntlLduTarget intlLduTarget = new IntlLduTarget();
            Map<String, HashMap<String, Long>> resultMap = statisticsMapper.statisticReportLog(statistics);
            if (CollUtil.isNotEmpty(resultMap)) {
                // 使用 Optional 避免空指针
                Optional.ofNullable(resultMap.get("actualCoveredStores"))
                        .map(map -> map.get("metric_value"))
                        .ifPresent(value -> intlLduTarget.setActualCoveredStores(value.intValue()));

                Optional.ofNullable(resultMap.get("actualDisplayCount"))
                        .map(map -> map.get("metric_value"))
                        .ifPresent(value -> intlLduTarget.setActualSampleOut(value.intValue()));

                Integer actualCoveredStores = resultMap.get("actualCoveredStores").get("metric_value").intValue();
                Integer actualDisplayCount = resultMap.get("actualDisplayCount").get("metric_value").intValue();
                intlLduTarget.setActualCoveredStores(actualCoveredStores);
                intlLduTarget.setActualSampleOut(actualDisplayCount);
                intlLduTarget.setProductId(statistics.getProductId());
                intlLduTarget.setRetailerCode(statistics.getRetailerCode());
                intlLduTarget.setCountryCode(statistics.getCountryCode());
                intlLduTarget.setUpdateUserId(userBaseInfo.getUserId());
                intlLduTarget.setUpdateUserName(userBaseInfo.getUserAccount());
                intlLduTarget.setTargetUpdateDate(System.currentTimeMillis());
                intlLduTargetList.add(intlLduTarget);
            }
        }
        intlLduTargetMapper.batchUpdate(intlLduTargetList);
    }

    private static void createLduSnData(RmsUserBaseDataResponse userBaseInfo, SnImeiGoodsInfoReq goodsInfoDto, List<String> finalSnList,
                                        IntlLduReportLog statistics, List<IntlLduSnDto> intlLduSnList, Map<String, IntlLduSnDto> snMap) {
        // SN不在LDU 计划表里面，进行值填充，插入
        if (CollUtil.isEmpty(finalSnList)) {
            IntlLduSnDto intlLduSn = new IntlLduSnDto();
            intlLduSn.setStatus(ValidityStatusEnum.VALID.getCode());
            intlLduSn.setIsReport(ReportStatusEnum.NEW_UNPLANNED_REPORT.getCode());
            intlLduSn.setPlanCreateDate(System.currentTimeMillis());
            intlLduSn.setCreateUserId(userBaseInfo.getUserId());
            intlLduSn.setCreateUserName(userBaseInfo.getEnglishName());
            intlLduSn.setGoodsId(goodsInfoDto.getGoodsId());
            intlLduSn.setGoodsName(goodsInfoDto.getGoodsName());
            intlLduSn.setLduType(LduTypeEnum.MASS_PRODUCTION_VERSION.getType());

            statistics.setPlanStatus(YesOrNoEnum.NO.getCode());

            BeanUtils.copyProperties(statistics, intlLduSn);
            intlLduSnList.add(intlLduSn);
        } else if (CollUtil.isNotEmpty(finalSnList) && !finalSnList.contains(goodsInfoDto.getSn())) {
            IntlLduSnDto intlLduSn = new IntlLduSnDto();
            intlLduSn.setStatus(ValidityStatusEnum.VALID.getCode());
            intlLduSn.setIsReport(ReportStatusEnum.NEW_UNPLANNED_REPORT.getCode());
            intlLduSn.setPlanCreateDate(System.currentTimeMillis());
            intlLduSn.setCreateUserId(userBaseInfo.getUserId());
            intlLduSn.setCreateUserName(userBaseInfo.getEnglishName());
            intlLduSn.setGoodsId(goodsInfoDto.getGoodsId());
            intlLduSn.setGoodsName(goodsInfoDto.getGoodsName());

            statistics.setPlanStatus(YesOrNoEnum.NO.getCode());

            BeanUtils.copyProperties(statistics, intlLduSn);
            intlLduSnList.add(intlLduSn);
        } else {
            statistics.setPlanStatus(YesOrNoEnum.YES.getCode());
            if (CollUtil.isNotEmpty(snMap) && snMap.containsKey(goodsInfoDto.getSn())) {
                statistics.setLduType(snMap.get(goodsInfoDto.getSn()).getLduType());
            }
        }
    }

    private static void createOnlineUpload(SnImeiGoodsInfoReq goodsInfoDto,
                                           IntlLduReportLog statistics,
                                           List<IntlFileUpload> intlFileUploads,
                                           RmsUserBaseDataResponse userBaseInfo) {
        FileUploadReq uploadPhotos = goodsInfoDto.getUploadPhotos();
        if (uploadPhotos != null) {
            for (FileUploadReq.UPDetail upDetail : uploadPhotos.getUpDetails()) {
                IntlFileUpload intlFileUpload = new IntlFileUpload();
                BeanUtils.copyProperties(uploadPhotos, intlFileUpload);
                intlFileUpload.setRelatedId(statistics.getReportId());

                if (CharSequenceUtil.isNotBlank(upDetail.getFdsUrl())) {
                    intlFileUpload.setFdsUrl(upDetail.getFdsUrl());
                    intlFileUpload.setSuffix(getFileSuffix(upDetail.getFdsUrl()));
                }
                intlFileUpload.setModuleName(FileUploadEnum.LDU_UPLOAD.getCode());
                intlFileUpload.setGuid(upDetail.getGuid());
                intlFileUpload.setIsNoWatermark(0);
                intlFileUpload.setIsOfflineUpload(0);
                intlFileUpload.setIsUploadedToBlob(0);
                intlFileUpload.setCreateTime(System.currentTimeMillis());
                intlFileUpload.setUpdateTime(System.currentTimeMillis());
                intlFileUpload.setUploaderName(userBaseInfo.getUserAccount());
                intlFileUpload.setUploaderTime(System.currentTimeMillis());
                intlFileUpload.setSuffix("");
                intlFileUploads.add(intlFileUpload);
            }
        }
    }

    public static String getFileSuffix(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        int lastDotIndex = url.lastIndexOf(".");
        int lastSlashIndex = url.lastIndexOf("/");

        if (lastDotIndex > lastSlashIndex && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex + 1); // 返回类似 ".jpg"
        }

        return "";
    }

    private static IntlLduReportLog createtIntlLduReportLog(IntlLduReportSubmitReq intlLduReportDataReq,
                                                            RmsUserBaseDataResponse userBaseInfo, SnImeiGoodsInfoReq goodsInfoDto,
                                                            IntlRmsCountryTimezone intlRmsCountryTimezone, String countryCode,
                                                            StoreInfoDTO intlRmsStore) {
        long id = IdUtil.getSnowflakeNextId();

        IntlLduReportLog statistics = new IntlLduReportLog();

        BeanUtils.copyProperties(goodsInfoDto, statistics);

        statistics.setReportId(id);
        statistics.setRegion(intlRmsCountryTimezone.getAreaCode());
        statistics.setRegionCode(intlRmsCountryTimezone.getArea());
        statistics.setCountry(intlRmsCountryTimezone.getCountryName());
        statistics.setCountryCode(countryCode);
        statistics.setRetailerCode(intlRmsStore.getRetailerIdName());
        statistics.setRetailerName(intlRmsStore.getRetailerName());
        statistics.setChannelType(intlRmsStore.getChannelTypeName());
        statistics.setStoreCode(intlRmsStore.getCode());
        statistics.setStoreName(intlRmsStore.getStoreName());
        statistics.setProvince(intlRmsStore.getProvinceName());
        statistics.setCity(intlRmsStore.getCityIdName());
        statistics.setDistrict(intlRmsStore.getCountyName());
        statistics.setProductName(goodsInfoDto.getGoodsNameEn());
        statistics.setColor(goodsInfoDto.getEnglishColor());
        statistics.setProductLine(goodsInfoDto.getProductLineEn());

        statistics.setRamCapacity(goodsInfoDto.getRam());
        statistics.setRomCapacity(goodsInfoDto.getRom());
        statistics.setCode69(goodsInfoDto.getCode69());
        statistics.setImei1(goodsInfoDto.getImei());
        statistics.setProductId(goodsInfoDto.getGoodsId());
        statistics.setDisplayStatus(DisplayStatusEnum.ON_DISPLAY.getCode());
        statistics.setIsDelete(YesOrNoEnum.YES.getCode());
        statistics.setReportRole(userBaseInfo.getJobTitle());
        statistics.setReportDistance(intlLduReportDataReq.getReportDistance());

        statistics.setCreateUserId(userBaseInfo.getUserId());
        statistics.setCreateUserName(userBaseInfo.getEnglishName());
        statistics.setUpdateUserId(userBaseInfo.getUserId());
        statistics.setUpdateUserName(userBaseInfo.getEnglishName());
        statistics.setCreateTime(System.currentTimeMillis());
        statistics.setUpdateTime(System.currentTimeMillis());
        return statistics;
    }

    private List<String> updateLduSn(String countryCode, List<IntlLduSnDto> intlLduSns) {
        List<String> snList = null;
        if (CollUtil.isNotEmpty(intlLduSns)) {
            snList = intlLduSns.stream()
                    .map(IntlLduSnDto::getSn)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            intlLduSnService.batchUpdateBySn(intlLduSns, countryCode);
        }
        return snList;
    }

    private static void offlineeUpload(List<SnImeiGoodsInfoReq> goodsList, List<IntlFileUpload> intlFileUploads) {
        for (SnImeiGoodsInfoReq goodsInfoDto : goodsList) {

            FileUploadReq uploadPhotos = goodsInfoDto.getUploadPhotos();
            if (uploadPhotos != null) {
                for (FileUploadReq.UPDetail upDetail : uploadPhotos.getUpDetails()) {
                    IntlFileUpload intlFileUpload = new IntlFileUpload();
                    BeanUtils.copyProperties(uploadPhotos, intlFileUpload);
                    if (CharSequenceUtil.isNotBlank(upDetail.getFdsUrl())) {
                        intlFileUpload.setFdsUrl(upDetail.getFdsUrl());
                    }
                    intlFileUpload.setUpdateTime(System.currentTimeMillis());
                    intlFileUpload.setGuid(upDetail.getGuid());
                    intlFileUploads.add(intlFileUpload);
                }
            }
        }
    }

    @Override
    public CommonResponse<SnImeiGoodsInfoDto> scanBarcode(BarcodeScanReq request) {

        if (request.getImageData() == null || request.getImageData().isEmpty()) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Image data cannot be empty");
        }

        try {
            String imageData = request.getImageData();
            // 处理可能的数据URL前缀
            if (imageData.startsWith("data:image")) {
                imageData = imageData.substring(imageData.indexOf(",") + 1);
            }

            byte[] imageBytes = Base64.getDecoder().decode(imageData);
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));

            if (bufferedImage == null) {
                throw new IllegalArgumentException("Invalid image format");
            }

            // 图像预处理（可选）
            bufferedImage = preprocessImage(bufferedImage);

            LuminanceSource source = new BufferedImageLuminanceSource(bufferedImage);
            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));

            Map<DecodeHintType, Object> hints = new HashMap<>();
            hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
            hints.put(DecodeHintType.POSSIBLE_FORMATS, Arrays.asList(BarcodeFormat.UPC_A,
                    BarcodeFormat.UPC_E, BarcodeFormat.EAN_13, BarcodeFormat.EAN_8,
                    BarcodeFormat.CODE_39, BarcodeFormat.CODE_93, BarcodeFormat.CODE_128,
                    BarcodeFormat.ITF, BarcodeFormat.RSS_14, BarcodeFormat.RSS_EXPANDED));

            Result result = new MultiFormatReader().decode(bitmap, hints);
            ScanCodeReq scanCodeReq = new ScanCodeReq();
            scanCodeReq.setSerialNumber(result.getText());

            return scan(scanCodeReq);
        } catch (NotFoundException e) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Barcode not found");
        } catch (Exception e) {
            return CommonResponse.failure(ResultCodeEnum.PARAM_ERROR.getCode(), "Failed to parse barcode");
        }
    }

    @Override
    public CommonResponse<StroeInfoDto> getStoreInfo(RmsPositionReq request) {
        log.info("getStoreInfo request = {}", JSONUtil.toJsonStr(request));
        if (StrUtil.isEmpty(request.getPositionCode())) {
            throw new BusinessException("Sales point information does not exist");
        }
        IntlRmsStore storeInfo = intlRmsPositionMapper.getStoreInfo(request);

        if (storeInfo == null) {
            throw new BusinessException("Sales point information does not exist");
        }
        StroeInfoDto storeInfoDto = new StroeInfoDto();
        storeInfoDto.setChannelType(storeInfo.getChannelType());
        storeInfoDto.setChannelTypeDesc(storeInfo.getChannelTypeName());
        storeInfoDto.setStoreName(storeInfo.getName());
        storeInfoDto.setStoreId(storeInfo.getStoreId());
        return new CommonResponse<>(storeInfoDto);
    }

    private BufferedImage preprocessImage(BufferedImage original) {
        // 图像预处理逻辑（如转为灰度、调整对比度等）
        BufferedImage processed = new BufferedImage(original.getWidth(), original.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        processed.getGraphics().drawImage(original, 0, 0, null);
        return processed;
    }


}
