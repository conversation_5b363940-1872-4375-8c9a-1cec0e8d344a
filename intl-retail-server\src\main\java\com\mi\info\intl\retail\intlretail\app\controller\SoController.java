package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.QtyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetFilterListResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.provider.ImeiUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/so")
public class SoController {
    @Resource
    private ImeiUploadService imeiUploadService;
    @Resource
    private QtyService qtyService;
    @Resource
    private ImeiReportVerifyService imeiReportVerifyService;



    @PostMapping("/submitImei")
    @ResponseBody
    public CommonApiResponse<Object> submitImei(@RequestBody SubmitImeiReq request) {
        log.info("submitImei request: {}", request);
        return imeiUploadService.submitImei(request);
    }

    @PostMapping("/imeiReportVerify")
    @ResponseBody
    public CommonApiResponse<Object> imeiReportVerify(@RequestBody ImeiReportVerifyRequest request) {
        return imeiReportVerifyService.imeiReportVerify(request);
    }

    @PostMapping("/getSkuList")
    @ResponseBody
    public CommonResponse<GetSkuListResponse> getSkuList(@RequestBody GetSkuListRequest request) {
        log.info("getSkuList request: {}", request);
        return qtyService.getSkuList(request);
    }

    @PostMapping("/submitQty")
    @ResponseBody
    public CommonResponse<Object> submitQty(@RequestBody SubmitQtyReq request) {
        log.info("submitQty request: {}", request);
        return qtyService.submitQty(request);
    }

    @PostMapping("/getFilterList")
    @ResponseBody
    public CommonResponse<GetFilterListResponse> getFilterList() {
        log.info("getFilterList request");
        return qtyService.getFilterList();
    }

    @PostMapping("/queryImeiListByPage")
    @ResponseBody
    public CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(@RequestBody ImeiListQueryReq request) {
        log.info("queryImeiListByPage request: {}", request);
        return imeiUploadService.queryImeiListByPage(request);
    }

    @PostMapping("/queryImeiDetail")
    @ResponseBody
    public CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(@RequestBody ImeiDetailQueryReq request) {
        log.info("queryImeiDetail request: {}", request);
        return imeiUploadService.queryImeiDetail(request);
    }

    @PostMapping("/queryImeiSummary")
    @ResponseBody
    public CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(@RequestBody ImeiSummaryQueryReq request) {
        log.info("queryImeiSummary request: {}", request);
        return imeiUploadService.queryImeiSummary(request);
    }
}
