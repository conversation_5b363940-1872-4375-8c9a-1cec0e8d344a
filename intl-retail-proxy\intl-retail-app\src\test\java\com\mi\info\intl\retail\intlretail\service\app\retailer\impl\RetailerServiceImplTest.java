package com.mi.info.intl.retail.intlretail.service.app.retailer.impl;

import com.mi.info.intl.retail.intlretail.service.api.retailer.RetailerService;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.app.retailer.constant.RetailerChannelTypeEnum;
import com.mi.info.intl.retail.intlretail.service.app.retailer.constant.RetailerPositionCodeEnum;
import com.mi.info.intl.retail.intlretail.service.app.retailer.constant.RetailerPositionTypeEnum;
import com.mi.info.intl.retail.intlretail.service.app.retailer.constant.StoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class RetailerServiceImplTest {

    @InjectMocks
    private RetailerServiceImpl retailerService;

    @Mock
    private IGateWayChannelInfoService gateWayChannelInfoService;

    @Resource
    private RetailerService retailerServiceTest;

    @Test
    public void test_getArea_Success() {
        // 执行方法
        RetailerAreaResponse response = retailerService.getArea();

        // 验证 ChannelList
        List<ChannelDto> channelList = response.getChannelList();
        assertEquals(RetailerChannelTypeEnum.values().length, channelList.size());
        for (int i = 0; i < RetailerChannelTypeEnum.values().length; i++) {
            RetailerChannelTypeEnum typeEnum = RetailerChannelTypeEnum.values()[i];
            ChannelDto channelDto = channelList.get(i);
// 为了解决 assertEquals 方法的歧义问题，明确指定参数类型为 Object
            assertEquals((Object) typeEnum.getId(), (Object) channelDto.getChannelId());
            assertEquals(typeEnum.getName(), channelDto.getChannelName());
        }

        // 验证 PositionCodeList
        List<PositionDto> positionCodeList = response.getPositionCodeList();
        assertEquals(RetailerPositionCodeEnum.values().length, positionCodeList.size());
        for (int i = 0; i < RetailerPositionCodeEnum.values().length; i++) {
            RetailerPositionCodeEnum codeEnum = RetailerPositionCodeEnum.values()[i];
            PositionDto positionDto = positionCodeList.get(i);
// 为了解决 assertEquals 方法的歧义问题，明确指定参数类型为 Object
            assertEquals((Object) codeEnum.getId(), (Object) positionDto.getPositionId());
            assertEquals(codeEnum.getName(), positionDto.getPositionName());
        }

        // 验证 PositionTypeList
        List<PositionDto> positionTypeList = response.getPositionTypeList();
        assertEquals(RetailerPositionTypeEnum.values().length, positionTypeList.size());
        for (int i = 0; i < RetailerPositionTypeEnum.values().length; i++) {
            RetailerPositionTypeEnum typeEnum = RetailerPositionTypeEnum.values()[i];
            PositionDto positionDto = positionTypeList.get(i);
// 为了解决 assertEquals 方法的歧义问题，明确指定参数类型为 Object
            assertEquals((Object) typeEnum.getOrgId(), (Object) positionDto.getPositionId());
            assertEquals(typeEnum.getName(), positionDto.getPositionName());
        }
    }

    @Test
    public void test_getRetailerInfo_SingleCode_Success() {
        // 准备测试数据
        String region = "testRegion";
        String code = "testCode";
        RetailerInfoRequest request = new RetailerInfoRequest();
        request.setRegion(region);
        request.setCodes(Arrays.asList(code));

        ChannelInfoResponse channelInfoResponse = new ChannelInfoResponse();
// 由于 ChannelInfoResponse.Basic 无法解析为类型，需要检查是否缺少该内部类的定义
// 这里假设存在替代的类或者创建新的类来存储相同信息
// 若有对应的替代类，需要替换下面的类名
// 这里暂时使用一个简单的自定义类来模拟
        class BasicInfo {
            private String code;
            private String name;

            public void setCode(String code) {
                this.code = code;
            }

            public void setName(String name) {
                this.name = name;
            }
        }
        BasicInfo basic = new BasicInfo();
        basic.setCode(code);
        basic.setName("testName");
// 创建 ChannelInfoResponse.BasicDTO 实例并赋值
        ChannelInfoResponse.BasicDTO basicDTO = new ChannelInfoResponse.BasicDTO();
// 由于 getCode() 方法未定义，假设使用 code 字段直接赋值
        basicDTO.setCode(basic.code);
// 由于 getName() 方法未定义，直接使用 basic 对象的 name 字段
        basicDTO.setName(basic.name);
        channelInfoResponse.setBasic(basicDTO);
        List<ChannelInfoResponse> channelInfoResponses = new ArrayList<>();
        channelInfoResponses.add(channelInfoResponse);

        GateWayChannelInfoResponse gateWayResponse = new GateWayChannelInfoResponse();
        gateWayResponse.setData(channelInfoResponses);

        // Mock 方法调用
        when(gateWayChannelInfoService.queryChannelInfo(argThat(new ArgumentMatcher<ChannelInfoRequest>() {
            @Override
            public boolean matches(ChannelInfoRequest argument) {
                if (argument == null) return false;
                return region.equals(argument.getAreaId()) &&
                        region.equals(argument.getCountryCode()) &&
                        code.equals(argument.getSearch()) &&
                        StoreTypeEnum.RETAILER.getId() == argument.getType();
            }
        }))).thenReturn(gateWayResponse);

        // 执行方法
        List<RetailerInfoResponse> result = retailerService.getRetailerInfo(request);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals(code, result.get(0).getCode());
        assertEquals("testName", result.get(0).getName());
    }

    @Test
    public void test_getRetailerInfo_MultipleCodes_Success() {
        // 准备测试数据
        String region = "testRegion";
        List<String> codes = Arrays.asList("code1", "code2");
        RetailerInfoRequest request = new RetailerInfoRequest();
        request.setRegion(region);
        request.setCodes(codes);

        List<ChannelInfoResponse> channelInfoResponses = new ArrayList<>();
        for (String code : codes) {
            ChannelInfoResponse channelInfoResponse = new ChannelInfoResponse();
// 由于 ChannelInfoResponse.Basic 无法解析为类型，参考之前的处理方式，使用自定义类模拟
            class BasicInfo {
                private String code;
                private String name;

                public void setCode(String code) {
                    this.code = code;
                }

                public void setName(String name) {
                    this.name = name;
                }
            }
            BasicInfo basic = new BasicInfo();
            basic.setCode(code);
            basic.setName("name_" + code);
// 创建 ChannelInfoResponse.BasicDTO 实例并将 BasicInfo 的数据赋值给它
            ChannelInfoResponse.BasicDTO basicDTO = new ChannelInfoResponse.BasicDTO();
            basicDTO.setCode(basic.code);
            basicDTO.setName(basic.name);
            channelInfoResponse.setBasic(basicDTO);
            channelInfoResponses.add(channelInfoResponse);
        }

        GateWayChannelInfoResponse gateWayResponse = new GateWayChannelInfoResponse();
        gateWayResponse.setData(channelInfoResponses);

        // Mock 方法调用
        when(gateWayChannelInfoService.queryChannelInfo(argThat(new ArgumentMatcher<ChannelInfoRequest>() {
            @Override
            public boolean matches(ChannelInfoRequest argument) {
                if (argument == null) return false;
                return region.equals(argument.getAreaId()) &&
                        region.equals(argument.getCountryCode()) &&
                        codes.equals(argument.getCodes()) &&
                        StoreTypeEnum.RETAILER.getId() == argument.getType();
            }
        }))).thenReturn(gateWayResponse);

        // 执行方法
        List<RetailerInfoResponse> result = retailerService.getRetailerInfo(request);

        // 验证结果
        assertEquals(2, result.size());
        for (int i = 0; i < codes.size(); i++) {
            assertEquals(codes.get(i), result.get(i).getCode());
            assertEquals("name_" + codes.get(i), result.get(i).getName());
        }
    }

    @Test
    public void test_getRetailerInfo_NoDataFound() {
        // 准备测试数据
        String region = "testRegion";
        String code = "testCode";
        RetailerInfoRequest request = new RetailerInfoRequest();
        request.setRegion(region);
        request.setCodes(Arrays.asList(code));

        GateWayChannelInfoResponse gateWayResponse = new GateWayChannelInfoResponse();
        gateWayResponse.setData(new ArrayList<>());

        // Mock 方法调用
        when(gateWayChannelInfoService.queryChannelInfo(argThat(new ArgumentMatcher<ChannelInfoRequest>() {
            @Override
            public boolean matches(ChannelInfoRequest argument) {
                if (argument == null) return false;
                return region.equals(argument.getAreaId()) &&
                        region.equals(argument.getCountryCode()) &&
                        code.equals(argument.getSearch()) &&
                        StoreTypeEnum.RETAILER.getId() == argument.getType();
            }
        }))).thenReturn(gateWayResponse);

        // 执行方法
        List<RetailerInfoResponse> result = retailerService.getRetailerInfo(request);

        // 验证结果
        assertEquals(0, result.size());
    }

    @Test
    public void test_getRetailerInfo_NullRequest() {
        // 执行方法
        List<RetailerInfoResponse> result = retailerService.getRetailerInfo(null);

        // 验证结果
        assertEquals(0, result.size());
    }


    // 测试 getOrgPerson 方法正常流程
    @Test
    public void test_getOrgPerson_Success() {
        // 准备测试数据
        BusinessDataInputRequest request = new BusinessDataInputRequest();
        List<BusinessDataResponse> mockResponses = new ArrayList<>();
        mockResponses.add(new BusinessDataResponse());

        // 模拟 gateWayChannelInfoService 的行为
        when(gateWayChannelInfoService.queryBusinessData(argThat(new ArgumentMatcher<BusinessDataInputRequest>() {
            @Override
            public boolean matches(BusinessDataInputRequest argument) {
                return argument != null;
            }
        }))).thenReturn(mockResponses);

        // 执行方法
        List<BusinessDataResponse> responses = retailerService.getOrgPerson(request);

        // 验证结果
        assertEquals(mockResponses, responses);
    }

    // 测试 getOrgPerson 方法在 gateWayChannelInfoService 抛出异常的情况
    @Test(expected = RuntimeException.class)
    public void test_getOrgPerson_Exception() {
        // 准备测试数据
        BusinessDataInputRequest request = new BusinessDataInputRequest();

        // 模拟 gateWayChannelInfoService 抛出异常
        when(gateWayChannelInfoService.queryBusinessData(argThat(new ArgumentMatcher<BusinessDataInputRequest>() {
            @Override
            public boolean matches(BusinessDataInputRequest argument) {
                return argument != null;
            }
        }))).thenThrow(new RuntimeException("Mocked exception"));

        // 执行方法，期望抛出异常
        retailerService.getOrgPerson(request);
    }

}