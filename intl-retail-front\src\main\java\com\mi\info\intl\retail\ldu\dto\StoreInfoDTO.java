package com.mi.info.intl.retail.ldu.dto;

import lombok.Data;

@Data
public class StoreInfoDTO {

    /**
     * 零售商编码
     */
    private String retailerIdName;
   /**
     * 零售商名称
     */
    private String retailerName;
    /**
     * 渠道类型名称
     */
    private String channelTypeName;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市名称
     */
    private String cityIdName;
    /**
     * 区县名称
     */
    private String countyName;
    /**
     * 门店编码
     */
    private String code;

}
