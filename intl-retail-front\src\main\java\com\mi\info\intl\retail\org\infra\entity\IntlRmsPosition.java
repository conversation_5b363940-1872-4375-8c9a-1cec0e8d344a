package com.mi.info.intl.retail.org.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RMS阵地表
 *
 * @TableName intl_rms_position
 */
@TableName(value = "intl_rms_position")
@Data
public class IntlRmsPosition implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一标识
     */
    @TableField(value = "position_id")
    private String positionId;

    /**
     * 阵地代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 阵地名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 门店名称
     */
    @TableField(value = "store_id")
    private String storeId;

    /**
     * 门店名称标签
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 简称
     */
    @TableField(value = "abbreviation")
    private String abbreviation;

    /**
     * 运营状态
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 运营状态标签
     */
    @TableField(value = "state_name")
    private String stateName;

    /**
     * 供应商
     */
    @TableField(value = "distributor_id")
    private String distributorId;

    /**
     * 供应商标签
     */
    @TableField(value = "distributor_name")
    private String distributorName;

    /**
     * 分销商
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 分销商标签
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 零售商代码
     */
    @TableField(value = "retailer_id")
    private String retailerId;

    /**
     * 零售商代码标签
     */
    @TableField(value = "retailer_name")
    private String retailerName;

    /**
     * 渠道类型
     */
    @TableField(value = "channel_type")
    private Integer channelType;

    /**
     * 渠道类型标签
     */
    @TableField(value = "channel_type_name")
    private String channelTypeName;

    /**
     * 阵地类型
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 阵地类型标签
     */
    @TableField(value = "type_name")
    private String typeName;

    /**
     * 门店等级
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 门店等级标签
     */
    @TableField(value = "level_name")
    private String levelName;

    /**
     * 是否有促
     */
    @TableField(value = "is_promotion_store")
    private Integer isPromotionStore;

    /**
     * 国家/地区
     */
    @TableField(value = "country_id")
    private String countryId;

    /**
     * 国家/地区标签
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 城市
     */
    @TableField(value = "city_id")
    private String cityId;

    /**
     * 城市标签
     */
    @TableField(value = "city_name")
    private String cityName;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 修改时间
     */
    @TableField(value = "modified_on")
    private Date modifiedOn;

    /**
     * 创建时间
     */
    @TableField(value = "created_on")
    private Date createdOn;

    /**
     * 负责人
     */
    @TableField(value = "owner_id")
    private String ownerId;

    /**
     * 负责人标签
     */
    @TableField(value = "owner_name")
    private String ownerName;

    /**
     * 是否可用
     */
    @TableField(value = "state_code")
    private Integer stateCode;


    /**
     * 区域
     */
    @TableField(value = "area")
    private String area;

    /**
     * 区域代码
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 门店阵地编码
     */
    @TableField(value = "crpscode")
    private String crpsCode;

    /**
     * 阵地品类
     */
    @TableField(value = "position_category")
    private String positionCategory;

    /**
     * 家具数量
     */
    @TableField(value = "furniture_ttl")
    private Integer furnitureTtl;

    /**
     * 阵地展陈标准化打标信息
     */
    @TableField(value = "display_capacity_expansion_status")
    private Integer displayCapacityExpansionStatus;

    /**
     * 阵地落位
     */
    @TableField(value = "position_location")
    private Integer positionLocation;

    /**
     * 阵地打卡经度
     */
    @TableField(value = "position_longitude")
    private String positionLongitude;

    /**
     * 阵地打卡纬度
     */
    @TableField(value = "position_latitude")
    private String positionLatitude;

    private Long createdAt;
    private Long updatedAt;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}