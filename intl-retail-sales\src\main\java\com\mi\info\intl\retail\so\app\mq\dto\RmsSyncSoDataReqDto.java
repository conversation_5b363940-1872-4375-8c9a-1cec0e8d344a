package com.mi.info.intl.retail.so.app.mq.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RmsSyncSoDataReqDto<T extends Serializable> implements Serializable {


    private static final long serialVersionUID = 1558828309223295528L;

    private T data;

    /**
     * 取值：
     * imei
     * qty
     */
    private String type;


    /**
     * 取值：
     * create
     * report verification
     * activate verification
     */
    private String operateType;

    /**
     * 更新的字段集合
     */
    private List<String> fields;
}
