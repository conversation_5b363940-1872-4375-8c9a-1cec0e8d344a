### START_MIT ###
logs
./logs
.log
test-output/
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Mac ###
.DS_Store
``
### Java ###
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*
replay_pid*

### Maven ###
.mvn/
mvnw
mvnw.cmd
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

### Gradle ###
.gradle/
gradle-app.setting
!gradle-wrapper.jar
.gradletasknamecache

### Node ###
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

### Python ###
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

### Docker ###
.dockerignore
docker-compose.override.yml

### Misc ###
*.swp
*.swo
*~

### Home Work Logs ###
home/work/log/

.cursor
.specstory/**
log/*
log/*