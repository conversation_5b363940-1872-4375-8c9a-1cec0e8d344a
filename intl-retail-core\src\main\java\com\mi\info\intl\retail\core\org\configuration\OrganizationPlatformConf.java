package com.mi.info.intl.retail.core.org.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 组织平台conf
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "organization")
public class OrganizationPlatformConf {

    private String scene;

    /**
     * manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
     * organMap:
     * 256:
     * positionId: 256
     * organCode: GLOBAL
     * positionName: 总部策略运营
     * manageChannelList:
     * - 27
     * 242:
     * positionId: 242
     * organCode: null
     * positionName: 国家零售经理
     * manageChannelList:
     * - 27
     * 255:
     * positionId: 255
     * organCode: GLOBAL
     * positionName: 总部策略管理
     * manageChannelList:
     * - 27
     */
    private Map<String, JobInfo> organMap;

}
