package com.mi.info.intl.retail.converter;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述：转换父类
 *
 * <AUTHOR>
 * @date 2025/8/1
 */
public abstract class AbstractConvert<S, T> {

    /**
     * 返回多个目标对象
     *
     * @param sources
     * @return List<TARGET>
     */
    public List<T> toTarget(List<S> sources) {
        if (null == sources) {
            return new ArrayList<>();
        }
        List<T> target = new ArrayList<>(sources.size());
        for (S source : sources) {
            target.add(toTarget(source));
        }
        return target;
    }

    /**
     * 返回单个目标对象
     *
     * @param source
     * @return TARGET
     */
    public T toTarget(S source) {
        if (null == source) {
            return null;
        }
        return populateTarget(source);
    }

    /**
     * 填充目标对象
     *
     * @param source
     * @return TARGET
     */
    protected abstract T populateTarget(S source);


}
