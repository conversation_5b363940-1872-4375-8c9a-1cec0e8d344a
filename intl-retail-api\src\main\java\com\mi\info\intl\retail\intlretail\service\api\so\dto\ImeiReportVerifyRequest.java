package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * IMEI上报验证请求DTO
 */
@Data
public class ImeiReportVerifyRequest implements Serializable {

    /**
     * imei或者sn集合
     */
    @JsonProperty("sns")
    private List<String> sns;

    /**
     * 用户职位value
     */
    @JsonProperty("userTitle")
    private int userTitle;

    /**
     * 用户miid
     */
    @JsonProperty("miId")
    private String miId;

    /**
     * 国家编码
     */
    @JsonProperty("countryCode")
    private String countryCode;
}