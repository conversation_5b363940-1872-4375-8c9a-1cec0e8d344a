### START_MIT ###
include:
  - project: mit/infra/ci-templates
    file: maven/maven.gitlab-ci.yml
    ref: master

variables:
  # 镜像相关参数
  # CI_REGISTRY_PROJECT: "" # 镜像仓库项目名称 (可选)
  CI_JDK_MAJOR: "8" # JD<PERSON> 大版本, 支持: 8, 17, 21. (可选, 默认 8)
  # CI_RUNTIME_JDK_MAJOR: "" # 支持单独指定运行时 JDK 大版本.
  # 测试环境部署相关
  # CD_MATRIX_PROJECT: "" # Matrix/IAM 项目的 节点ID 或 节点路径 比如 xiaomi.info.xxx.xxx.xxx
  # CD_MATRIX_DEPLOY_SPACE: "" # Matrix 部署空间名称
  # 参考文档
  HELP_DOC: https://xiaomi.f.mioffice.cn/docs/dock4sxibajPCr6nTAtSHHbN8Xg# #多环境单一镜像CICD流程


