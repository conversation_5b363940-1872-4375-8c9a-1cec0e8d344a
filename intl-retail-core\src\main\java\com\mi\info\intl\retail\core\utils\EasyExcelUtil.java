package com.mi.info.intl.retail.core.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.ReadListener;
import org.springframework.stereotype.Component;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2025/7/29
 **/
@Component
public class EasyExcelUtil {
    
    /**
     *
     * @param inputStream   输入流
     * @param clazz         导入文件行数据对应的对象
     * @param listener      导入的监听者
     * @param headRowNumber 表头
     * @param <T>           行数据对应的对象类型
     */
    public <T> void readFromStream(InputStream inputStream, Class<T> clazz, ReadListener<T> listener, int headRowNumber) {
        
        EasyExcel.read(inputStream, clazz, listener)
                .sheet()
                .headRowNumber(headRowNumber)
                .doRead();
    }
    
}
