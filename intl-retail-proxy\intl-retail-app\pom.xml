<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mi.info.intl.retail</groupId>
        <artifactId>intl-retail-proxy</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>intl-retail-app</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>0.0.1-SNAPSHOT</revision>
        <galaxy-fds-sdk-java.version>3.0.41</galaxy-fds-sdk-java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-infra</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-server-registry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-fds-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>store-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>xmstore-action-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>prometheus-starter</artifactId>
                    <groupId>com.xiaomi.youpin</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cnzone</groupId>
            <artifactId>maindata-api</artifactId>
        </dependency>

        <dependency>
            <artifactId>proretail-bi-api</artifactId>
            <groupId>com.xiaomi.cnzone</groupId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.intl.retail</groupId>
            <artifactId>intl-retail-fieldforce</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>global-nr-dev-common</artifactId>
        </dependency>
        <!-- 添加组织中台依赖 -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>nr-eiam-api</artifactId>
            <version>2.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                    <groupId>com.alibaba.cloud</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>