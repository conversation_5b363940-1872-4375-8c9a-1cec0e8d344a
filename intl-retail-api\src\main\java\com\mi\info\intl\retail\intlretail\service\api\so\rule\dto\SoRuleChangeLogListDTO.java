package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * so规则详情返回参数DTO
 *
 * <AUTHOR>
 */
@Data
public class SoRuleChangeLogListDTO implements Serializable {

    /**
     * 主键ID
     */
    @ApiDocClassDefine("主键ID")
    private Long id;
    /**
     * 审批单ID
     */
    @ApiDocClassDefine("审批单ID")
    private String approvalId;

    /**
     * 状态(0:草稿,1:审批中,2:审批通过,3:审批未通过,4:撤回)
     */
    @ApiDocClassDefine("状态")
    private Integer status;

    /**
     * 申请人
     */
    @ApiDocClassDefine("申请人")
    private String applicant;

    /**
     * 更新时间
     */
    @ApiDocClassDefine("更新时间")
    private Long applicationTime;

}
