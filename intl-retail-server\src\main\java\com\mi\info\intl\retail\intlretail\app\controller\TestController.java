package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.mq.RmsSyncDbService;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController extends BaseController {

    @Resource
    private RmsSyncDbService rmsSyncDbService;

    @PostMapping("/syncRmsDb")
    @ResponseBody
    public String syncRmsDbMsg(@RequestBody RmsDbRequest request) {


        CommonResponse commonResponse = rmsSyncDbService.syncRmsDbMsg(request);
        return commonResponse.getMessage();

    }

}