package com.mi.info.intl.retail.api.file.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 图片数据信息DTO
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
@Getter
@Setter
public class PhotoDataInfoDTO {
    private Long relatedId;
    private Integer isOfflineUpload;
    private Integer isUploadedToBlob;
    private String moduleName;
    private String uploaderName;
    private Long uploaderTime;
    private String fdsUrl;
    private Long createTime;
    private Long updateTime;
    private String suffix;
    private String guid;
}
