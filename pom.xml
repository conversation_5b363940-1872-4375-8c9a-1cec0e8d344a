<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaomi.mit</groupId>
        <artifactId>parent-pom</artifactId>
        <version>1.2.9</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.mi.info.intl.retail</groupId>
    <artifactId>intl-retail</artifactId>
    <version>${revision}</version>

    <packaging>pom</packaging>

    <name>intl-retail</name>
    <description>International Retail</description>

    <modules>
        <module>intl-retail-api</module>
        <module>intl-retail-inner-api</module>
        <module>intl-retail-common</module>
        <module>intl-retail-core</module>

        <module>intl-retail-front</module>
        <module>intl-retail-fieldforce</module>
        <module>intl-retail-sales</module>
        <module>intl-retail-cooperation</module>

        <module>intl-retail-proxy</module>
        <module>intl-retail-server</module>
    </modules>
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>0.0.1-SNAPSHOT</revision>
        <comb-dependencies.version>2.1.17-RELEASE</comb-dependencies.version>
        
        <cola.components.version>4.0.0</cola.components.version>

        <spring.boot.version>2.7.4</spring.boot.version>
        <spring-boot.version>2.7.4</spring-boot.version>

        <org.mapstruct.version>1.5.2.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.30</org.projectlombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <rocketmq.version>2.2.0-mdh2.2.7-RELEASE</rocketmq.version>
        <neptune_env>test</neptune_env>
        <nacos-client.version>1.2.1-mone-SNAPSHOT</nacos-client.version>
        <dubbo.version>2.7.12-mone-v20-SNAPSHOT</dubbo.version>
        <spring-cloud-azure-dependencies.version>4.14.0</spring-cloud-azure-dependencies.version>
        <store.version>1.5-SNAPSHOT</store.version>
        <brain-platform-api.version>2.1-RELEASE</brain-platform-api.version>
        <mybatis.version>3.5.14</mybatis.version>
        <mybatis-spring.version>2.1.2</mybatis-spring.version>
        <mybatis-plus.version>3.5.4.1</mybatis-plus.version>
        <http-docs.version>2.7.12-mone-v10-SNAPSHOT</http-docs.version>
        <miapi-doc-annos.version>2.7.12-mone-v20-SNAPSHOT</miapi-doc-annos.version>
        <okhttp.version>4.10.0</okhttp.version>
    </properties>

    <repositories>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>releases</id>
            <name>maven-release</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/releases</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>miremote</id>
            <name>maven-remote-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>maven-snapshots</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/snapshots</url>
        </repository>
        <repository>
            <id>snapshot-virtual</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-dto</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                <version>2.7.18</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>http-docs-core</artifactId>
                <version>${http-docs.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>miapi-doc-annos</artifactId>
                <version>${miapi-doc-annos.version}</version>
            </dependency>
            <!-- 本项目包管理 start -->
            <dependency>
                <artifactId>market-api</artifactId>
                <groupId>market</groupId>
                <version>1.0.5.8-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-api</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-infra</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-app</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-cooperation</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-fieldforce</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-front</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.intl.retail</groupId>
                <artifactId>intl-retail-sales</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- 本项目包管理 end -->

            <dependency>
                <groupId>com.mi.info.comb</groupId>
                <artifactId>comb-dependencies</artifactId>
                <version>${comb-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 三方公共包管理 start -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
                <version>2.7.4</version>
            </dependency>
            <dependency>
                <groupId>com.azure.spring</groupId>
                <artifactId>spring-cloud-azure-dependencies</artifactId>
                <version>${spring-cloud-azure-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.azure.spring</groupId>
                <artifactId>spring-cloud-azure-starter-active-directory</artifactId>
                <version>4.14.0</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>1.0.10</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>dubbo-server-registry</artifactId>
                <version>2.7.12-mone-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>1.2.1-mone-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>0.3.6-mone-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-context-support</artifactId>
                        <groupId>com.alibaba.spring</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>youpin-infra-rpc</artifactId>
                <version>2.1.6</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>msg-push-api</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>9.2.0</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.10.1</version>
            </dependency>

            <dependency>
                <groupId>com.xiaomi.infra.galaxy</groupId>
                <artifactId>galaxy-fds-sdk-java</artifactId>
                <version>${galaxy-fds-sdk-java.version}</version>
            </dependency>
            <!-- 三方公共包管理 end -->

            <!-- 其他项目二方包管理 start-->
            <dependency>
                <groupId>com.xiaomi.cnzone</groupId>
                <artifactId>maindata-api</artifactId>
                <version>1.9-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.cnzone</groupId>
                <artifactId>store-api</artifactId>
                <version>${store.version}</version>
            </dependency>
            <!--    对接文档：https://xiaomi.f.mioffice.cn/docx/doxk4DhLNOTZ4QkKyxAtES79KFd        -->
            <dependency>
                <groupId>com.xiaomi.cnzone</groupId>
                <artifactId>xmstore-action-api</artifactId>
                <version>2.0-RELEASE</version>
            </dependency>
            <dependency>
                <artifactId>proretail-bi-api</artifactId>
                <groupId>com.xiaomi.cnzone</groupId>
                <version>1.2.4-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.cnzone</groupId>
                <artifactId>brain-platform-api</artifactId>
                <version>${brain-platform-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>aop</artifactId>
                        <groupId>com.xiaomi.youpin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bpm-api</artifactId>
                        <groupId>com.xiaomi.newretail</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>coloregg</artifactId>
                        <groupId>com.xiaomi.youpin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common</artifactId>
                        <groupId>com.xiaomi.youpin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-docs-core</artifactId>
                        <groupId>com.xiaomi.mone</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>validation-api</artifactId>
                        <groupId>javax.validation</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                        <groupId>com.alibaba.cloud</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.22</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.11</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>global-nr-dev-common</artifactId>
                <version>1.1-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

        <!-- lombok & mapstruct start-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.2.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.2.Final</version>
        </dependency>
        <!-- lombok & mapstruct end -->
        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
            <version>1.2.9</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <package.env>dev</package.env>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>sg-test</id>
            <properties>
                <package.env>sg-test</package.env>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>cn-test</id>
            <properties>
                <package.env>cn-test</package.env>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>sg-pre</id>
            <properties>
                <package.env>sg-pre</package.env>
                <spring.profiles.active>pre</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>sg-prod</id>
            <properties>
                <package.env>sg-prod</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>cn-prod</id>
            <properties>
                <package.env>cn-prod</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>uae-prod</id>
            <properties>
                <package.env>uae-prod</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>latin-prod</id>
            <properties>
                <package.env>latin-prod</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>eu-prod</id>
            <properties>
                <package.env>eu-prod</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>uae-pre</id>
            <properties>
                <package.env>uae-pre</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>latin-pre</id>
            <properties>
                <package.env>latin-pre</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>eu-pre</id>
            <properties>
                <package.env>eu-pre</package.env>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.mi.xms</groupId>
                    <artifactId>neptune-maven-plugin</artifactId>
                    <version>1.0-SNAPSHOT</version>
                    <executions>
                        <execution>
                            <phase>compile</phase>
                            <goals>
                                <goal>translate</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <appId>605cfb858b254dd89f4d5d49b4ddcb33</appId>
                        <env>${neptune_env}</env>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${org.projectlombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${lombok-mapstruct-binding.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.version}</version>
                    <!-- <configuration>
                        <excludes>
                            <exclude>com/mi/info/intl/retail/intlretail/infra/**/*Test.java</exclude>
                        </excludes>
                    </configuration> -->
                </plugin>

            </plugins>
        </pluginManagement>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.yml</include>
                    <include>logback*.xml</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources/${package.env}</directory>
                <filtering>true</filtering>
                <targetPath>${project.build.directory}/classes</targetPath>
            </resource>
        </resources>
    </build>

</project>
