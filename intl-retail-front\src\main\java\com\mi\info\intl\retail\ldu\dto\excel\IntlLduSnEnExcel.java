package com.mi.info.intl.retail.ldu.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
public class IntlLduSnEnExcel {

    /**
     * 区域
     */
    @ExcelProperty("Region")
    private String region;

    /**
     * 区域编码
     */
    @ExcelProperty("Region Code")
    private String regionCode;

    /**
     * 国家
     */
    @ExcelProperty("Country")
    private String country;

    /**
     * 国家/地区编码
     */
    @ExcelProperty("Country/Region Code")
    private String countryCode;

    /**
     * 渠道类型
     */
    @ExcelProperty("Channel Type")
    private String channelType;

    /**
     * 零售商编码
     */
    @ExcelProperty("Retailer Code")
    private String retailerCode;

    /**
     * 零售商名称
     */
    @ExcelProperty("Retailer Name")
    private String retailerName;

    /**
     * LDU类型:大货（Mass Production Version）、专样（Customized Version）
     */
    @ExcelProperty("LDU Type")
    private String lduType;

    /**
     * 产品线
     */
    @ExcelProperty("Product Line")
    private String productLine;

    /**
     * 产品ID
     */
    @ExcelProperty("Product ID")
    private String goodsId;

    /**
     * 产品名称
     */
    @ExcelProperty("Product Name")
    private String goodsName;

    /**
     * 项目代码
     */
    @ExcelProperty("Project Code")
    private String projectCode;

    /**
     * RAM容量
     */
    @ExcelProperty("RAM")
    private String ramCapacity;

    /**
     * 计划创建日期
     */
    @ExcelProperty("Creation Time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planCreateDate;


    /**
     * IMEI 1
     */
    @ExcelProperty("IMEI 1")
    private String imei1;

    /**
     * ROM容量
     */
    @ExcelProperty("ROM")
    private String romCapacity;

    /**
     * 序列号SN
     */
    @ExcelProperty("SN")
    private String sn;

    /**
     * IMEI 2
     */
    @ExcelProperty("IMEI 2")
    private String imei2;

    /**
     * 创建人ID
     */
    @ExcelProperty("Creator ID")
    private String createUserId;

    /**
     * 创建人姓名
     */
    @ExcelProperty("Creator Name")
    private String createUserName;


    /**
     * 2新增计划外上报，0未核销  1已核销
     */
    @ExcelProperty("Is Reported")
    private String isReport;

    /**
     * 状态:1有效，0是无效
     */
    @ExcelProperty("Status")
    private String status;

}
