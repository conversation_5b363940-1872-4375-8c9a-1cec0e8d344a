package com.mi.info.intl.retail.core.org.service;

import com.mi.info.intl.retail.core.org.configuration.JobInfo;
import com.mi.info.intl.retail.utils.RpcResultUtils;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetParentOrganPositionUserReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrganizePlatformServiceImpl implements OrganizePlatformService {

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserProvider.class, check = false,
            timeout = 5000)
    private UserProvider userProvider;

    @Value("${organization.scene:new_retail}")
    private String defaultScene;

    @Override
    public List<UserPosition> getOrganizePlatform(JobInfo jobInfo) {
        GetParentOrganPositionUserReq userInfoRequest =
                buildGetParentOrganPositionUserReq(jobInfo);
        return RpcResultUtils.handleOrgRpc(
                userProvider::getParentOrganPositionUser,
                userInfoRequest,
                "UserProvider.getParentOrganPositionUser",
                true).orElse(Collections.emptyList());
    }

    private GetParentOrganPositionUserReq buildGetParentOrganPositionUserReq(JobInfo jobInfo) {
        GetParentOrganPositionUserReq userInfoRequest = new GetParentOrganPositionUserReq();

        userInfoRequest.setScene(StringUtils.isBlank(jobInfo.getScene()) ? defaultScene : jobInfo.getScene());
        userInfoRequest.setOrganCode(jobInfo.getOrganCode());
        userInfoRequest.setPositionId(jobInfo.getPositionId());
        userInfoRequest.setManageChannelList(jobInfo.getManageChannelList());
        return userInfoRequest;
    }

    /**
     * 批量获取组织平台岗位信息
     *
     * @param jobInfoList 工作信息清单
     * @return {@link Map }<{@link String }, {@link List }<{@link UserPosition }>>
     */
    @Override
    public Map<Integer, List<UserPosition>> getBatchOrganizePlatform(List<JobInfo> jobInfoList) {
        if (CollectionUtils.isEmpty(jobInfoList) || jobInfoList.size() > 5) {
            throw new IllegalArgumentException("jobInfoList is empty or size > 5");
        }

        return jobInfoList.stream().collect(Collectors.toMap(JobInfo::getPositionId,
                jobInfo -> CompletableFuture.supplyAsync(() -> getOrganizePlatform(jobInfo)).join()));

    }

}
