package com.mi.info.intl.retail.core.es;

import java.lang.annotation.*;

import org.springframework.data.elasticsearch.annotations.FieldType;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface EsQuery {
    String field();  // 查询字段名

    FieldType type() default FieldType.Auto;  // 字段类型

    QueryType queryType() default QueryType.MATCH;  // 查询类型

    String analyzer() default "";  // 分词器

    boolean required() default false;  // 是否必须条件

    enum QueryType {
        MATCH,
        TERM,
        TERMS,
        RANGE,
        WILDCARD
    }
}
