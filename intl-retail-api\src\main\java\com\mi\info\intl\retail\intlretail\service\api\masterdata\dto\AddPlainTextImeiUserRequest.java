package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 新增明文用户
 *
 * <AUTHOR>
 * @date 2025/7/31
 **/
@Data
public class AddPlainTextImeiUserRequest implements Serializable {
    
    private static final long serialVersionUID = -3241973778489085918L;
    
    /**
     * 国家代码
     */
    @ApiDocClassDefine(value = "国家代码")
    private String countryCode;

    /**
     * 国家名称
     */
    @ApiDocClassDefine(value = "国家名称")
    private String countryName;


    /**
     * miId
     */
    @ApiDocClassDefine(value = "miId")
    private Long miId;

    /**
     * 图片URL,最多上传三张
     */
    @ApiDocClassDefine(value = "图片URL,最多上传三张")
    private List<String> photoUrlList;
}
