package com.mi.info.intl.retail.org.infra.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【intl_rms_country_timezone(国家时区表)】的数据库操作Mapper
* @createDate 2025-06-05 10:36:14
* @Entity generator.domain.IntlRmsCountryTimezone
*/
@Mapper
public interface IntlRmsCountryTimezoneMapper extends BaseMapper<IntlRmsCountryTimezone> {

    List<IntlRmsCountryTimezone> selectAll();

    IntlRmsCountryTimezone selectByCountryCode(@Param("countryCode") String countryCode);
}




