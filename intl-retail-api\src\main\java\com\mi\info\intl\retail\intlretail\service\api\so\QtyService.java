package com.mi.info.intl.retail.intlretail.service.api.so;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetFilterListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitQtyReq;

/**
 * QTY服务接口
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
public interface QtyService {

    /**
     * 可售SKU查询
     *
     * @param request 查询请求
     * @return 可售SKU列表
     */
    CommonResponse<GetSkuListResponse> getSkuList(GetSkuListRequest request);

    /**
     * QTY提交
     *
     * @param request 提交请求
     * @return 提交结果
     */
    CommonResponse<Object> submitQty(SubmitQtyReq request);

    /**
     * 获取筛选列表
     *
     * @return 筛选列表
     */
    CommonResponse<GetFilterListResponse> getFilterList();
} 