package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * so规则详情返回参数DTO
 *
 * <AUTHOR>
 */
@Data
public class SoRuleChangeLogDTO implements Serializable {

    /**
     * id
     */
    @ApiDocClassDefine("id")
    private Long id;

    /**
     * 区域编码
     */
    @ApiDocClassDefine("区域编码")
    private String regionCode;

    /**
     * 国家编码
     */
    @ApiDocClassDefine("国家编码")
    private String countryCode;

    /**
     * 生效时间戳
     */
    @ApiDocClassDefine("生效时间戳")
    private Long effectiveTime;

    /**
     * 状态
     */
    @ApiDocClassDefine("状态")
    private Integer status;

    /**
     * 照片规则列表
     */
    @ApiDocClassDefine("照片规则列表")
    private List<PhotoRuleDTO> photoRuleList;

    /**
     * IMEI规则列表
     */
    @ApiDocClassDefine("IMEI规则列表")
    private List<ImeiRuleDTO> imeiRuleList;

    /**
     * 审批人列表
     */
    @ApiDocClassDefine("审批人列表")
    private List<ApproverDTO> approverList;

    /**
     * 给bpm使用详细消息
     */
    private String bpmBody;

    /**
     * 创建日期
     */
    @ApiDocClassDefine("创建日期")
    private Long createdAt;

    /**
     * 创建人ID
     */
    @ApiDocClassDefine("创建人ID")
    private String createdBy;

    /**
     * 修改人ID
     */
    @ApiDocClassDefine("修改人ID")
    private String updatedBy;

    /**
     * 修改日期
     */
    @ApiDocClassDefine("修改日期")
    private Long updatedAt;
}
