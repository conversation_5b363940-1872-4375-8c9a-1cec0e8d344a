package com.mi.info.intl.retail.intlretail.service.api.so.sales.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述：Verification Result 返回dto
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class VerificationResultRespDto implements Serializable {

    private static final long serialVersionUID = -5116578129530588949L;

    @ApiDocClassDefine("Verifying Count")
    private Long verifyingCount;

    @ApiDocClassDefine("Successfully Count")
    private Long successfullyCount;

    @ApiDocClassDefine("Failed Count")
    private Long failedCount;

}
