package com.mi.info.intl.retail.cooperation.task.app.mq;

import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlBigPromotionConf;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlBigPromotionConfReadMapper;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.CurrentTaskInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.FinishUserCurrentEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ResetUserCurrentTaskEventStatusReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskInstanceIdAndEventDefinitionIdReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskInstanceIdReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.EventInstanceListResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.EventInstanceResp;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskInstanceResp;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.NullArgumentException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.inspection.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.inspection.topic}", consumerGroup = "${intl-retail.rocketmq.inspection.group}", enableMsgTrace = true)
public class RmsApiRequestConsumer implements RocketMQListener<String> {
    
    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformOuterProvider brainPlatformOuterProvider;
    
    @Autowired
    private IntlBigPromotionConfReadMapper intlBigPromotionConfReadMapper;
    
    @Autowired
    private InspectionConfig inspectionConfig;
    
    @Override
    public void onMessage(String message) {
        log.info("RmsApiRequestConsumer message:{}", message);
        try {
            RmsAipRequestInfo rmsAipRequestInfo = Optional.ofNullable(
                            JsonUtil.json2bean(message, RmsAipRequestInfo.class))
                    .orElseThrow(() -> new NullArgumentException("rmsAipRequestInfo is null"));
            
            String countryCode = rmsAipRequestInfo.getCountryCode();
            Date countryLocalDate = IntlTimeUtil.getCountryLocalDateByCountryCode(countryCode);
            TaskInstanceResp.TaskInstance taskInstance = queryUserCurrentTaskInstance(rmsAipRequestInfo);
            if (taskInstance == null) {
                log.warn("task not exist, ignore this action msg: {}", rmsAipRequestInfo);
                return;
            }
            SRTaskActionEnum srTaskActionEnum = SRTaskActionEnum.fromActionName(rmsAipRequestInfo.getType());
            if (srTaskActionEnum == null) {
                log.warn("action type is invalid, ignore this action msg: {}", rmsAipRequestInfo);
                return;
            }
            
            IntlBigPromotionConf conf = intlBigPromotionConfReadMapper.queryCurrentBigPromotionTask(countryCode,
                    countryLocalDate);
            
            if (conf == null) {
                log.info("do no big promotion task, action name: {}, action code: {} ",
                        srTaskActionEnum.getActionName(), srTaskActionEnum.getActionCode());
                handleTaskActionNoPromotion(rmsAipRequestInfo, taskInstance);
            } else {
                log.info("do big promotion task, action name: {}, action code: {} ", srTaskActionEnum.getActionName(),
                        srTaskActionEnum.getActionCode());
                handleTaskActionInPromotion(rmsAipRequestInfo, taskInstance, conf);
            }
        } catch (Exception e) {
            throw new RuntimeException("RmsApiRequestConsumer.onMessage error", e);
        }
    }
    
    private boolean inCurrentCountryDate(String countryCode, Long finishTime) {
        List<Long> startAndEndOfDayMillis = IntlTimeUtil.getStartAndEndOfDayMillis(countryCode);
        if (startAndEndOfDayMillis == null) {
            log.error("get start and end of day millis error, countryCode:{}", countryCode);
            return false;
        }
        Long startOfDayMillis = startAndEndOfDayMillis.get(0);
        Long endOfDayMillis = startAndEndOfDayMillis.get(1);
        return finishTime >= startOfDayMillis && finishTime <= endOfDayMillis;
    }
    
    /**
     * 无新品任务时的处理
     */
    private void handleTaskActionNoPromotion(RmsAipRequestInfo info, TaskInstanceResp.TaskInstance instance) {
        if (instance.getStatus() == 1) {
            log.info("task has finished, ignore this action msg: {}", info);
        }
        SRTaskActionEnum actionEnum = SRTaskActionEnum.fromActionName(info.getType());
        if (actionEnum == SRTaskActionEnum.CHECK_OUT_SR) {
            handleSignOut(info, instance);
        } else if (actionEnum == SRTaskActionEnum.CHECK_IN_SR) {
            handleSignIn(info, instance);
        } else {
            finishUserCurrentTask(info, null);
        }
    }
    
    /**
     * 有新品任务时的处理
     */
    private void handleTaskActionInPromotion(RmsAipRequestInfo info, TaskInstanceResp.TaskInstance instance,
            IntlBigPromotionConf conf) {
        if (instance.getStatus() == 1 && !inCurrentCountryDate(info.getCountryCode(), instance.getFinishTimeStamp())) {
            log.warn("task has finished(not current day finished),ignore this action msg:{}", info);
            return;
        }
        SRTaskActionEnum currentActionEnum = SRTaskActionEnum.fromActionName(info.getType());
        if (instance.getStatus() != 1 && arrivedPromotionCompletedCondition(currentActionEnum, instance)) {
            Map<String, String> customRecordMap = new HashMap<>();
            customRecordMap.put("bigPromotionStartTime", IntlTimeUtil.toFormatString(conf.getStartTime()));
            customRecordMap.put("bigPromotionEndTime", IntlTimeUtil.toFormatString(conf.getEndTime()));
            // completeType 2: 完成动作，主动完成子任务
            finishUserCurrentTask(info, customRecordMap, 2);
        } else {
            // completeType 1: 完成动作，子任务非主动完成(默认被动完成逻辑)
            finishUserCurrentTask(info, null, 1);
        }
    }
    
    private boolean arrivedPromotionCompletedCondition(SRTaskActionEnum currentActionEnum,
            TaskInstanceResp.TaskInstance instance) {
        List<Long> mustDoEvent = inspectionConfig.getMustDoEventDefinitionIds();
        List<Long> anyOneEvent = inspectionConfig.getAnyOneEventDefinitionIds();
        List<EventInstanceResp> eventInstances = queryEventInstanceList(instance);
        Map<Long, EventInstanceResp> eventInstanceMap = eventInstances.stream()
                .collect(Collectors.toMap(EventInstanceResp::getEventDefinitionId, Function.identity()));
        boolean mustDoEventAllCompleted = false;
        Long currentEventId = currentActionEnum.getActionCode();
        boolean anyOneEventCompleted = anyOneEvent.contains(currentEventId);
        if (anyOneEventCompleted) {
            int mustDoEventCount = mustDoEvent.size();
            for (Long mustDoEventId : mustDoEvent) {
                EventInstanceResp eventInstanceResp = eventInstanceMap.get(mustDoEventId);
                // 不存在视为完成，则减1;存在且完成则减1
                if (eventInstanceResp == null || eventInstanceResp.getStatus() == 1) {
                    mustDoEventCount--;
                }
            }
            mustDoEventAllCompleted = mustDoEventCount == 0;
        } else {
            int mustDoEventCount = mustDoEvent.size();
            for (Long mustDoEventId : mustDoEvent) {
                EventInstanceResp eventInstanceResp = eventInstanceMap.get(mustDoEventId);
                // 为当前事件则减1;不存在视为完成，则减1;存在且完成则减1
                if (currentEventId.equals(mustDoEventId) || eventInstanceResp == null
                        || eventInstanceResp.getStatus() == 1) {
                    mustDoEventCount--;
                }
            }
            mustDoEventAllCompleted = mustDoEventCount == 0;
            
            int anyOneEventCount = anyOneEvent.size();
            for (Long anyOneEventId : anyOneEvent) {
                EventInstanceResp eventInstanceResp = eventInstanceMap.get(anyOneEventId);
                //存在任意 "任意事件" 完成则 anyOneEventCompleted 为 true
                if (eventInstanceResp != null) {
                    anyOneEventCompleted = eventInstanceResp.getStatus() == 1;
                    if (anyOneEventCompleted) {
                        break;
                    }
                } else {
                    // 不存在任务时，anyOneEventCount 减1
                    anyOneEventCount--;
                }
            }
            if (!anyOneEventCompleted) {
                // 所有任意事件不存在场景： 任意事件不存在，则 anyOneEventCompleted 为 true
                anyOneEventCompleted = anyOneEventCount == 0;
            }
        }
        return anyOneEventCompleted && mustDoEventAllCompleted;
    }
    
    
    /**
     * SignOut 任务处理
     */
    private void handleSignOut(RmsAipRequestInfo info, TaskInstanceResp.TaskInstance instance) {
        // TODO: 优化 - 先获取所有动作事件实例，从列表里拿签入签出事件实例, 少一次IO
        EventInstanceResp signInEventInstance = querySignInEventInstance(instance.getId());
        if (signInEventInstance == null) {
            log.error("signInEventInstance is null, check this error, ignore this action msg: {}", info);
            return;
        }
        
        long signInTime = signInEventInstance.getFinishTimeStamp();
        if (signInTime == 0) {
            log.error("signInEventInstance signInTime is 0, check this error, ignore this action msg: {}", info);
            return;
        }
        
        // inspectionTime != 0 时 才管控 动作必须在 check in check out 之间 完成, 且时长要大于inspectionTime，否则重置动作状态
        // inspectionTime == 0 时 或 null 才不管控动作，不会重置动作状态
        Long inspectionTime = getInspectionTime(instance);
        Long costedInspectionTime = (System.currentTimeMillis() - signInTime) / (60 * 1000);
        if ((inspectionTime != null && inspectionTime != 0) && (costedInspectionTime < inspectionTime
                || !allActionCompleted(instance))) {
            resetUserCurrentTaskEventStatus(info);
        } else {
            Map<String, String> customRecordMap = new HashMap<>();
            customRecordMap.put("costedInspectionTime", String.valueOf(costedInspectionTime));
            finishUserCurrentTask(info, customRecordMap);
        }
    }
    
    
    /**
     * SignIn 任务处理
     */
    private void handleSignIn(RmsAipRequestInfo info, TaskInstanceResp.TaskInstance instance) {
        Long inspectionTime = getInspectionTime(instance);
        // inspectionTime != 0 时 才管控 动作必须在 check in check out 之间 完成, 且时长要大于inspectionTime，否则重置动作状态
        // inspectionTime == 0 时 或 null 才不管控动作，不会重置动作状态
        if (inspectionTime != null && inspectionTime != 0) {
            resetUserCurrentTaskEventStatus(info);
        }
        finishUserCurrentTask(info, null);
    }
    
    private boolean allActionCompleted(TaskInstanceResp.TaskInstance instance) {
        List<EventInstanceResp> eventInstances = queryEventInstanceList(instance);
        Long checkOutEventDefinitionId = SRTaskActionEnum.CHECK_OUT_SR.getActionCode();
        for (EventInstanceResp eventInstance : eventInstances) {
            Long eventDefinitionId = eventInstance.getEventDefinitionId();
            if (Objects.equals(eventDefinitionId, checkOutEventDefinitionId)) {
                continue;
            }
            if (eventInstance.getStatus() != 1) {
                return false;
            }
        }
        return true;
    }
    
    private TaskInstanceResp.TaskInstance queryUserCurrentTaskInstance(RmsAipRequestInfo info) {
        CurrentTaskInstanceReq currentTaskInstanceReq = new CurrentTaskInstanceReq();
        currentTaskInstanceReq.setMid(info.getMid());
        currentTaskInstanceReq.setOrgId(info.getPositionCode());
        currentTaskInstanceReq.setBusinessTypeId(SRTaskActionEnum.SR_INSPECTION.getActionCode());
        currentTaskInstanceReq.setRetailTenantId("2");
        currentTaskInstanceReq.setRetailAppSign("CHANNEL_RETAIL");
        Result<TaskInstanceResp.TaskInstance> result = brainPlatformOuterProvider.queryUserCurrentTaskInstance(
                currentTaskInstanceReq);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("queryUserCurrentTaskInstance error, result: {}", result);
            return null;
        }
        if (result.getData().getId() == null) {
            log.warn("task instance not exist, req: {}, result: {}", currentTaskInstanceReq, result);
            return null;
        }
        return result.getData();
    }
    
    private void resetUserCurrentTaskEventStatus(RmsAipRequestInfo info) {
        ResetUserCurrentTaskEventStatusReq resetUserCurrentTaskEventStatusReq = new ResetUserCurrentTaskEventStatusReq();
        resetUserCurrentTaskEventStatusReq.setMid(info.getMid());
        resetUserCurrentTaskEventStatusReq.setOrgId(info.getPositionCode());
        resetUserCurrentTaskEventStatusReq.setBusinessTypeId(SRTaskActionEnum.SR_INSPECTION.getActionCode());
        resetUserCurrentTaskEventStatusReq.setRetailTenantId("2");
        resetUserCurrentTaskEventStatusReq.setRetailAppSign("CHANNEL_RETAIL");
        brainPlatformOuterProvider.resetUserCurrentTaskEventStatus(resetUserCurrentTaskEventStatusReq);
    }
    
    private EventInstanceResp querySignInEventInstance(Long taskInstanceId) {
        TaskInstanceIdAndEventDefinitionIdReq taskInstanceIdAndEventDefinitionIdReq = new TaskInstanceIdAndEventDefinitionIdReq();
        taskInstanceIdAndEventDefinitionIdReq.setTaskInstanceId(taskInstanceId);
        taskInstanceIdAndEventDefinitionIdReq.setEventDefinitionId(SRTaskActionEnum.CHECK_IN_SR.getActionCode());
        Result<EventInstanceResp> result = brainPlatformOuterProvider.queryEventInstanceByTaskInstanceIdAndDefinitionId(
                taskInstanceIdAndEventDefinitionIdReq);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("querySignInEventInstance error, result: {}", result);
            return null;
        }
        if (result.getData().getId() == null) {
            log.warn("event instance not exist, req: {}, result: {}", taskInstanceIdAndEventDefinitionIdReq, result);
            return null;
        }
        return result.getData();
    }
    
    private boolean finishUserCurrentTask(RmsAipRequestInfo info, Map<String, String> customRecordMap) {
        return finishUserCurrentTask(info, customRecordMap, 1);
    }
    
    private boolean finishUserCurrentTask(RmsAipRequestInfo info, Map<String, String> customRecordMap,
            Integer completeType) {
        FinishUserCurrentEventReq finishUserCurrentEventReq = new FinishUserCurrentEventReq();
        finishUserCurrentEventReq.setMid(info.getMid());
        finishUserCurrentEventReq.setOrgId(info.getPositionCode());
        finishUserCurrentEventReq.setBusinessTypeId(SRTaskActionEnum.fromActionName(info.getType()).getActionCode());
        finishUserCurrentEventReq.setRetailTenantId("2");
        finishUserCurrentEventReq.setRetailAppSign("CHANNEL_RETAIL");
        finishUserCurrentEventReq.setOperateTimeStamp(System.currentTimeMillis());
        finishUserCurrentEventReq.setCompletedType(completeType);
        if (customRecordMap != null) {
            finishUserCurrentEventReq.setCustomRecordStr(JsonUtil.bean2json(customRecordMap));
        }
        Result<String> result = brainPlatformOuterProvider.finishUserCurrentEvent(finishUserCurrentEventReq);
        if (result.getCode() != 0) {
            log.error("finishUserCurrentTask error:{}", result.getMessage());
            return false;
        }
        return true;
    }
    
    
    private List<EventInstanceResp> queryEventInstanceList(TaskInstanceResp.TaskInstance instance) {
        TaskInstanceIdReq taskInstanceIdReq = new TaskInstanceIdReq();
        taskInstanceIdReq.setTaskInstanceId(instance.getId());
        Result<EventInstanceListResp> result = brainPlatformOuterProvider.queryEventInstanceByTaskInstanceId(
                taskInstanceIdReq);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("querySignInEventInstance error, result: {}", result);
            return new ArrayList<>();
        }
        return result.getData().getEventInstances();
    }
    
    private Long getInspectionTime(TaskInstanceResp.TaskInstance instance) {
        try {
            String customParamsStr = instance.getCustomParamsStr();
            Map<String, Object> customParamsMap = JsonUtil.json2map(customParamsStr);
            Integer inspectionTime = (Integer) customParamsMap.get("inspectionTime");
            if (inspectionTime == null) {
                return null;
            }
            return Long.valueOf(inspectionTime);
        } catch (Exception e) {
            log.error("getInspectionTime error", e);
            return null;
        }
    }
}