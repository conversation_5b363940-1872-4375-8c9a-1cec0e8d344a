package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取筛选列表响应DTO
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
public class GetFilterListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品线数组
     */
    private List<FilterItem> productLineList;

    /**
     * 门店类型数组
     */
    private List<FilterItem> storeTypeList;

    /**
     * 渠道类型数组
     */
    private List<FilterItem> channelTypeList;

    /**
     * 筛选项DTO
     */
    @Data
    public static class FilterItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 名称
         */
        private String name;

        /**
         * 代码
         */
        private Integer code;
    }
} 