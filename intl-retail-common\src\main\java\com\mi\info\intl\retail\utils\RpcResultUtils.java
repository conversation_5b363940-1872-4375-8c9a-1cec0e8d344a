package com.mi.info.intl.retail.utils;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;

import java.util.Optional;
import java.util.function.Function;

/**
 * RPC结果utils
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Slf4j
public class RpcResultUtils {

    public static <T, R> Optional<R> handleOrgRpc(Function<T, Result<R>> function, T param, String api,
                                                  boolean printRes) {
        Result<R> result = handleRpc(function, param, api, printRes);
        return Optional.ofNullable(result.getData());
    }

    private static <T, R> Result<R> handleRpc(Function<T, Result<R>> function, T param, String api, boolean printRes) {
        Result<R> result;
        try {
            long start = System.currentTimeMillis();

            // 执行调用
            result = function.apply(param);

            if (printRes) {
                log.info("handleRpc api:{} call finished, cost time:{} param:{} resp:{}",
                        api, System.currentTimeMillis() - start, JSON.toJSONString(param), JSON.toJSONString(result));
            } else {
                log.info("handleRpc api:{} call finished, cost time:{} param:{}",
                        api, System.currentTimeMillis() - start, JSON.toJSONString(param));
            }
        } catch (RpcException e) {
            log.error("handleRpc.RpcException api:{}, param:{}", api, JSON.toJSONString(param), e);
            if (e.isTimeout()) {
                throw new BizException(ErrorCodes.RPC_CALL_TIMEOUT, api);
            }
            throw new BizException(ErrorCodes.RPC_CALL_ERROR, api);
        } catch (Exception e) {
            log.error("handleRpc.Exception api:{}, param:{}", api, JSON.toJSONString(param), e);
            throw new BizException(ErrorCodes.RPC_CALL_ERROR, api);
        }

        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BizException(ErrorCodes.RPC_CALL_TIMEOUT, api, result.getMessage());
        }

        return result;
    }
}
