package com.mi.info.intl.retail.ldu.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class IntlLduReportExport implements Serializable {

    private static final long serialVersionUID = -765432109876543210L;

    @ExcelProperty("区域")
    private String region;

    @ExcelProperty("国家")
    private String country;

    @ExcelProperty("大区")
    private String bizRegion;

    @ExcelProperty("城市")
    private String opsCity;

    @ExcelProperty("网格")
    private String grid;

    @ExcelProperty("零售商编码")
    @ColumnWidth(20)
    private String retailerCode;

    @ExcelProperty("零售商名称")
    @ColumnWidth(20)
    private String retailerName;

    @ExcelProperty("渠道类型")
    @ColumnWidth(20)
    private String channelType;

    @ExcelProperty("门店编码")
    @ColumnWidth(20)
    private String storeCode;

    @ExcelProperty("门店名称")
    @ColumnWidth(20)
    private String storeName;

    @ExcelProperty("省")
    private String province;

    @ExcelProperty("市")
    private String city;

    @ExcelProperty("区")
    private String district;

    @ExcelProperty("产品线")
    @ColumnWidth(20)
    private String productLine;

    @ExcelProperty("Product ID")
    @ColumnWidth(20)
    private String productId;

    @ExcelProperty("Product Name")
    @ColumnWidth(100)
    private String productName;

    @ExcelProperty("项目代码")
    @ColumnWidth(20)
    private String projectCode;

    @ExcelProperty("RAM(G)")
    private String ramCapacity;

    @ExcelProperty("ROM(G)")
    private String romCapacity;

    @ExcelProperty("SN")
    private String sn;

    @ExcelProperty("69Code")
    private String code69;

    @ExcelProperty("IMEI 1")
    private String imei1;

    @ExcelProperty("IMEI 2")
    private String imei2;

    @ExcelProperty("是否计划内")
    @ColumnWidth(20)
    private String planStatusName;

    @ExcelProperty("LDU类型")
    private String lduType;

    @ExcelProperty("是否安装Mishow")
    private String mishowStatusName;

    @ExcelProperty("上一次获取到Mishow信息的时间")
    @ColumnWidth(40)
    private Date lastMishowFetchTime;

    @ExcelProperty("展陈状态")
    @ColumnWidth(20)
    private String displayStatusName;

    @ExcelProperty("上报距离差")
    @ColumnWidth(20)
    private BigDecimal reportDistance;

    @ExcelProperty("上报人姓名")
    @ColumnWidth(20)
    private String createUserName;

    @ExcelProperty("上报人ID")
    @ColumnWidth(20)
    private String createUserId;

    @ExcelProperty("上报角色")
    @ColumnWidth(20)
    private String reportRole;

    @ExcelProperty("上报时间")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("更新时间")
    @ColumnWidth(20)
    private Date updateTime;

    @ExcelProperty("LDU图片链接")
    @ColumnWidth(20)
    private String imageUrl;

}
