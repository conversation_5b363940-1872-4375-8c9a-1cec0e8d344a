package com.mi.info.intl.retail.intlretail.service.api.so;


import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;


/**
 * IMEI上报验证接口
 *
 * <AUTHOR>
 */
public interface ImeiReportVerifyService {

    /**
     * IMEI上报验证
     *
     * @return
     */
    CommonApiResponse<Object> imeiReportVerify(ImeiReportVerifyRequest request);
}