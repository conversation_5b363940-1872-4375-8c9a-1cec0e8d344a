package com.mi.info.intl.retail.intlretail.service.api.so.sales.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述：统计 返回dto
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class StatisticsRespDto implements Serializable {

    private static final long serialVersionUID = -5300891036352770714L;

    @ApiDocClassDefine("key")
    private String key;

    @ApiDocClassDefine("name")
    private String name;

    @ApiDocClassDefine("count")
    private Long count;


}
