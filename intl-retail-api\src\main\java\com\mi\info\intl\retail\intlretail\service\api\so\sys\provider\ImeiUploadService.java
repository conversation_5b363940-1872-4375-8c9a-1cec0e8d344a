package com.mi.info.intl.retail.intlretail.service.api.so.sys.provider;

import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiDetailQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiDetailQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiListQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiListQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiSummaryQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiSummaryQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitImeiReq;
import com.mi.info.intl.retail.model.CommonApiResponse;

/**
 * 销售上报服务
 *
 * <AUTHOR>
 * @date 2025/7/11 10:45
 */
public interface ImeiUploadService {

    /**
     * IMEI数据提交接口
     *
     * @param request 提交请求参数
     * @return 提交结果
     */
    CommonApiResponse<Object> submitImei(SubmitImeiReq request);

    /**
     * IMEI明细列表查询接口
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(ImeiListQueryReq request);

    /**
     * IMEI明细页查询接口
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(ImeiDetailQueryReq request);

    /**
     * IMEI汇总数据查询接口
     *
     * @param request 查询请求参数
     * @return 查询结果
     */
    CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(ImeiSummaryQueryReq request);
}
