package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * so规则审批回调请求参数DTO
 * <AUTHOR>
 * @date 2025/8/1 17:38
 */
@Data
public class SoRuleApproveCallbackDTO implements Serializable {

    private static final long serialVersionUID = 1436600768400735536L;
    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 审批人id
     */
    private String approveId;

    /**
     * 审批人名称
     */
    private String approveName;

    /**
     * 审批状态
     */
    private String status;
}
