package com.mi.info.intl.retail.ldu.enums;


import lombok.Getter;

/**
 * 是否计划内
 * 是否安装Mishow
 */
@Getter
public enum YesOrNoEnum {
    /**
     * 是
     */
    YES(0, "Yes", "是"),
    /**
     * 否
     */
    NO(1, "No", "否");

    private final int code;

    private final String value;

    private final String desc;

    YesOrNoEnum(int code, String value, String desc) {
        this.code = code;
        this.desc = desc;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据编码获取枚举
     */
    public static YesOrNoEnum getByCode(int code) {
        for (YesOrNoEnum enumValue : values()) {
            if (enumValue.code == code) {
                return enumValue;
            }
        }
        return null;
    }

}