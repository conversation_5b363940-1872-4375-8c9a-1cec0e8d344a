package com.mi.info.intl.retail.intlretail.service.api.so.rule.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 零售商规则统计
 *
 * <AUTHOR>
 * @date 2025/8/4 09:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SoRuleRetailerStatisticsDTO implements Serializable {

    private static final long serialVersionUID = -8433477822121563447L;
    /**
     * 国家对应零售商总数
     */
    private Integer totalRetailersCount;

    /**
     * 国家对应未配置规则零售商总数
     */
    private Integer noRuleRetailersCount;

    /**
     * 国家对应配置IMEI规则零售商总数
     */
    private Integer imeiRetailersCount;

    /**
     * 国家对应配置qty规则零售商总数
     */
    private Integer qtyRetailersCount;
}
