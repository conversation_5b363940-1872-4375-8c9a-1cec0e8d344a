package com.mi.info.intl.retail.ldu.util;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class NrJobTaskRequest<T> {
    /**
     * 任务Key（需与NrJob配置对应）
     */
    private String jobKey;
    /**
     * 任务负责人（通常为当前用户）
     */
    private String owner;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 任务参数（自动序列化为JSON）
     */
    private T taskParam;
    /**
     * 任务名称
     */
    private String taskName;
}
