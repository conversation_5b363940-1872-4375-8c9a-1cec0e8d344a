package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * IMEI上报验证响应DTO
 */
@Data
public class ImeiReportVerifyResponse implements Serializable {

    /**
     * 入参imei/sn
     */
    private String inputIMEI;

    /**
     * sn
     */
    private String sn;

    /**
     * imei1
     */
    private String imei1;

    /**
     * imei2
     */
    private String imei2;

    /**
     * 哈希Sn
     */
    private String snHash;

    /**
     * Sap Id
     */
    private Integer sapId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品SKU
     */
    private String productSku;

    /**
     * 校验结果
     * 0：成功
     * 1：产品校验失败
     * 2：重复性校验失败
     * 3：合法性校验失败
     */
    private Integer verifyResult;

    /**
    * 是否哈希国家
     * 0：否
     * 1：是
    */
    private Integer isHashCountry;
    /**
     * 销售渠道
     * 1：线上
     * 2：线下
     * 3：售后
     */
    private Integer saleChannel;
}