package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * IMEI明细列表查询响应参数
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@Data
public class ImeiListQueryResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否存在未返回的更多数据
     */
    @JsonProperty("moreRecords")
    private Boolean moreRecords;

    /**
     * 当前页数据涉及的日期汇总，而非全量日期汇总
     */
    @JsonProperty("dateGroupList")
    private List<DateGroupDto> dateGroupList;

    /**
     * IMEI明细数据
     */
    @JsonProperty("detailList")
    private List<ImeiDetailItemDto> detailList;

    /**
     * 日期汇总DTO
     */
    @Data
    public static class DateGroupDto implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 销售日期，格式为 yyyy-MM-dd
         */
        @JsonProperty("date")
        private String date;

        /**
         * 销售数量
         */
        @JsonProperty("count")
        private Integer count;
    }

    /**
     * IMEI明细项DTO
     */
    @Data
    public static class ImeiDetailItemDto implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 主键
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 产品名称
         */
        @JsonProperty("productName")
        private String productName;

        /**
         * IMEI1
         */
        @JsonProperty("imei1Mask")
        private String imei1Mask;

        /**
         * IMEI2
         */
        @JsonProperty("imei2Mask")
        private String imei2Mask;

        /**
         * SN
         */
        @JsonProperty("snMask")
        private String snMask;

        /**
         * 注释
         */
        @JsonProperty("note")
        private String note;

        /**
         * 门店名称
         */
        @JsonProperty("storeName")
        private String storeName;

        /**
         * IMEI校验结果code
         */
        @JsonProperty("verifyResult")
        private Integer verifyResult;

        /**
         * IMEI校验结果文本
         */
        @JsonProperty("verifyResultDetail")
        private String verifyResultDetail;

        /**
         * 销售日期
         */
        @JsonProperty("salesDate")
        private String salesDate;

        /**
         * 销售时间
         */
        @JsonProperty("salesTime")
        private String salesTime;
    }
}
