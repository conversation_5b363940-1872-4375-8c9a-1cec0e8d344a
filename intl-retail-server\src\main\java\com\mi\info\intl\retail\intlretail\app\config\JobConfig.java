package com.mi.info.intl.retail.intlretail.app.config;

import com.mi.info.intl.retail.ldu.config.LduConfig;
import com.xiaomi.nr.job.core.executor.impl.JobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 任务中心配置
 *
 * <AUTHOR>
 * @date 2021/11/15 16:56
 */
@Configuration
public class JobConfig {

    private Logger logger = LoggerFactory.getLogger(JobConfig.class);

    @Resource
    private LduConfig lduConfig;


    @Bean
    public JobSpringExecutor jobExecutor() {
        logger.info(">>>>>>>>>>> nr-job config init.");
        JobSpringExecutor jobExecutor = new JobSpringExecutor();
        jobExecutor.setAdminAddresses(lduConfig.getAddresses());
        jobExecutor.setAppname(lduConfig.getAppname());
        jobExecutor.setIp(getSystemProperty("TESLA_HOST"));
        jobExecutor.setPort(lduConfig.getPort());
        jobExecutor.setAccessToken(lduConfig.getAccessToken());
        jobExecutor.setLogPath(lduConfig.getLogPath());
        jobExecutor.setLogRetentionDays(lduConfig.getLogretentiondays());
        return jobExecutor;
    }

    public static String getSystemProperty(String key) {
        String value = System.getenv(key);
        if (value == null || value.length() == 0) {
            value = System.getProperty(key);
        }
        return value;
    }

}
