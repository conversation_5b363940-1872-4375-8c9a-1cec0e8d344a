package com.mi.info.intl.retail.cooperation.task.inspection;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser;

public interface IntlRmsUserService extends IService<IntlRmsUser> {

    IntlRmsUserDto getIntlRmsUserByDomainName(String domainName);
}
