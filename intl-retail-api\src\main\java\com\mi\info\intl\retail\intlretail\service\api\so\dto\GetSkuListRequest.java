package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 可售SKU查询请求
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Data
public class GetSkuListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 国家短代码
     */
    private String countryCode;

    /**
     * 用户GUID
     */
    private String userId;

    /**
     * 用户mid
     */
    private Long miId;

    /**
     * 职位code
     */
    private Long userTitle;

    /**
     * 模糊查询，支持 product name
     */
    private String search;

    /**
     * 产品线, 传空查全部
     */
    private String productLine;

    /**
     * 产品简称，传空查全部
     */
    private String shortName;

    /**
     * 产品名称，传空查全部
     */
    private String productName;
} 