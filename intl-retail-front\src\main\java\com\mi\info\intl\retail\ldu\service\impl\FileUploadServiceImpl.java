package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.api.file.FileUploadApiService;
import com.mi.info.intl.retail.api.file.dto.IntlFileUploadDto;
import com.mi.info.intl.retail.api.file.dto.PhotoDataInfoDTO;
import com.mi.info.intl.retail.ldu.converter.IntlFileUploadConverter;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件上传服务实现
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadApiService {

    @Resource
    private IntlFileUploadMapper intlFileUploadMapper;

    @Resource
    private IntlFileUploadConverter intlFileUploadConverter;

    @Override
    public int createPhotoData(List<PhotoDataInfoDTO> photoDataList) {
        log.info("创建图片数据, 数量: {}", photoDataList != null ? photoDataList.size() : 0);
        
        if (CollectionUtils.isEmpty(photoDataList)) {
            return 0;
        }
        
        try {
            // 转换为IntlFileUpload实体列表
            List<IntlFileUpload> fileUploadList = photoDataList.stream()
                    .map(this::convertToFileUpload)
                    .collect(Collectors.toList());
            
            // 使用batchInsertDatas方法批量插入
            int successCount = intlFileUploadMapper.batchInsertDatas(fileUploadList);
            
            log.info("创建图片数据成功, 成功创建{}条记录", successCount);
            
            return successCount;
            
        } catch (Exception e) {
            log.error("创建图片数据失败", e);
            return 0;
        }
    }

    /**
     * 转换PhotoDataInfoDTO为IntlFileUpload
     */
    private IntlFileUpload convertToFileUpload(PhotoDataInfoDTO photoData) {
        IntlFileUpload fileUpload = new IntlFileUpload();
        fileUpload.setRelatedId(photoData.getRelatedId());
        fileUpload.setIsOfflineUpload(photoData.getIsOfflineUpload());
        fileUpload.setIsUploadedToBlob(photoData.getIsUploadedToBlob());
        fileUpload.setModuleName(photoData.getModuleName());
        fileUpload.setUploaderName(photoData.getUploaderName());
        fileUpload.setUploaderTime(photoData.getUploaderTime());
        fileUpload.setFdsUrl(photoData.getFdsUrl());
        fileUpload.setCreateTime(photoData.getCreateTime());
        fileUpload.setUpdateTime(photoData.getUpdateTime());
        fileUpload.setSuffix(photoData.getSuffix());
        fileUpload.setGuid(photoData.getGuid());
        return fileUpload;
    }

    @Override
    public CommonApiResponse<List<IntlFileUploadDto>> getFileUploadListByRelatedIdAndModuleName(Long relatedId, String moduleName) {
        if (null == relatedId) {
            return CommonApiResponse.failure(500, "The parameter 'relatedId' is empty");
        }
        if (StringUtils.isEmpty(moduleName)) {
            return CommonApiResponse.failure(500, "The parameter 'moduleName' is empty");
        }
        List<IntlFileUpload> intlFileUploads = intlFileUploadMapper.selectByBusyIdAndMoudle(relatedId, moduleName);
        if (CollectionUtils.isEmpty(intlFileUploads)) {
            return CommonApiResponse.success(new ArrayList<>());
        }
        List<IntlFileUploadDto> intlFileUploadDtoList = intlFileUploadConverter.toEntity(intlFileUploads);
        return CommonApiResponse.success(intlFileUploadDtoList);
    }

}
