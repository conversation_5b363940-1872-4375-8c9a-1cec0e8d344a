package com.mi.info.intl.retail.intlretail.service.api.ldu;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BathConReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IntlLduSnService {

    CommonApiResponse<IPage<IntlLduSnDto>> pageList(IntlLduSnReq query);

    IPage<IntlLduSnDto> pageListSn(IntlLduSnReq query);

    CommonApiResponse<String> create(IntlLduSnDto confList);

    CommonResponse<String> exportPlanMaintenance(IntlLduSnReq query);

    CommonApiResponse<String> stopUse(IntlLduSnReq query);

    CommonApiResponse<List<String>> importStopPlanMaintenance(BathConReq query);

    CommonApiResponse<List<String>> importPlanMaintenance(BathConReq query);

    CommonApiResponse<String> downLoadLduTemp(BathConReq query);

    List<IntlLduSnDto> selectBySnAndCountryCode(List<SnImeiGoodsInfoReq> goodsList, String countryCode);

    void batchUpdateBySn(List<IntlLduSnDto> intlLduSns, String countryCode);

    void batchInsert(List<IntlLduSnDto> intlLduSnList);
}
