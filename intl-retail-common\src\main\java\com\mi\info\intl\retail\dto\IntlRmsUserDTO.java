package com.mi.info.intl.retail.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class IntlRmsUserDTO {

    /**
     * RMS用户唯一标识
     */
    private String rmsUserid;

    /**
     * 用户代码
     */
    private String code;

    /**
     * 用户账号
     */
    private String domainName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 国家
     */
    private String countryId;

    /**
     * 国家标签
     */
    private String countryName;

    /**
     * 用户职位
     */
    private Integer jobId;

    /**
     * 用户职位标签
     */
    private String jobName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 小米账号（Mi ID）
     */
    private Long miId;

    /**
     * 直属上级
     */
    private String managerId;

    /**
     * 直属上级标签
     */
    private String managerName;

    /**
     * 虚拟MID
     */
    private String virtualMiId;

    /**
     * 用户语言
     */
    private String languageId;

    /**
     * 用户语言标签
     */
    private String languageName;

    /**
     * 是否禁用
     */
    private Integer isDisabled;


}
