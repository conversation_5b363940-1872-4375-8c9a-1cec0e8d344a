nacos:
  namespace: public
  address: nacos://nacos.systech.b2c.srv:80
  config:
    address: nacos.systech.b2c.srv:80
init:
  # 这个不能动，否则不能上传，app的group已经预设好了
  group: release
  app:
    name: intl-retail-eu

store:
  dubbo:
    group: eu_online

xmstore:
  dubbo:
    group: eu-online

iib:
  dubbo:
    group: sgp_online_iib
push:
  dubbo:
    group: eu_online_push
proretailbi:
  dubbo:
    group: eu_online
center:
  dubbo:
    group: eu_online

cache:
  dubbo:
    group: eu_online_cache

college:
  dubbo:
    group: eu_online

eiam:
  dubbo:
    group: eu_online

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: 总部策略运营
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: 国家零售经理
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: 总部策略管理
      manageChannelList:
        - 27
  dubbo-group: eu_online