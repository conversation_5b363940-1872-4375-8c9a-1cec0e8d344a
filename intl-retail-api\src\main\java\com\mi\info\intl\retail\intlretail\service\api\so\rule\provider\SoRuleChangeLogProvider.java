package com.mi.info.intl.retail.intlretail.service.api.so.rule.provider;

import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogListDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleChangeLogReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleLogReq;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.List;

/**
 * 所以规则日志Dubbo接口
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
public interface SoRuleChangeLogProvider {

    /**
     * 获取SO规则详细日志列表
     *
     * @param req req
     * @return {@link Result }<{@link List }<{@link SoRuleChangeLogListDTO }>>
     */
    Result<List<SoRuleChangeLogListDTO>> getSoRuleChangeLogList(QuerySoRuleLogReq req);

    /**
     * 通过ID获取SO规则
     *
     * @param req req
     * @return {@link Result }<{@link SoRuleChangeLogDTO }>
     */
    Result<SoRuleChangeLogDTO> getSoRuleChangeLogById(QuerySoRuleChangeLogReq req);
}
