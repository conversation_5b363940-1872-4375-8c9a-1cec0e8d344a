package com.mi.info.intl.retail.intlretail.service.api.so.rule.request;

import com.mi.info.intl.retail.bean.BaseReq;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Getter;
import lombok.Setter;

/**
 * 查询so规则日志请求
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Getter
@Setter
public class QuerySoRuleChangeLogReq extends BaseReq {
    /**
     * 审批记录id
     */
    @ApiDocClassDefine("审批记录id")
    private Long id;
}
