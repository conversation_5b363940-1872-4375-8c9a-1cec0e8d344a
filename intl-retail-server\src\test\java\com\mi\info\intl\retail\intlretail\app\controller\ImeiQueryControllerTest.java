package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.provider.ImeiUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * IMEI查询接口测试
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@SpringBootTest(classes = TestApplication.class)
public class ImeiQueryControllerTest {

    @MockBean
    private ImeiUploadService imeiUploadService;

    private SoController soController;

    @BeforeEach
    void setUp() {
        soController = new SoController();
        // 通过反射设置私有字段
        try {
            java.lang.reflect.Field field = SoController.class.getDeclaredField("imeiUploadService");
            field.setAccessible(true);
            field.set(soController, imeiUploadService);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testQueryImeiListByPage_Success() {
        // 准备测试数据
        ImeiListQueryReq request = new ImeiListQueryReq();
        request.setCountryCode("CN");
        request.setUserId("test-user-id");
        request.setMiId(123456789L);
        request.setUserTitle(500900001L); // 促销员
        request.setPageIndex(1);
        request.setPageSize(10);

        ImeiListQueryResp mockResponse = new ImeiListQueryResp();
        mockResponse.setMoreRecords(false);
        mockResponse.setDateGroupList(new java.util.ArrayList<>());
        mockResponse.setDetailList(new java.util.ArrayList<>());

        // Mock服务返回
        when(imeiUploadService.queryImeiListByPage(any(ImeiListQueryReq.class)))
                .thenReturn(new CommonApiResponse<>(mockResponse));

        // 执行测试
        CommonApiResponse<ImeiListQueryResp> response = soController.queryImeiListByPage(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertFalse(response.getData().getMoreRecords());
    }

    @Test
    void testQueryImeiDetail_Success() {
        // 准备测试数据
        ImeiDetailQueryReq request = new ImeiDetailQueryReq();
        request.setImeiId("12345");
        request.setUserId("test-user-id");
        request.setMiId(123456789L);
        request.setUserTitle(500900001L);

        ImeiDetailQueryResp mockResponse = new ImeiDetailQueryResp();
        mockResponse.setId(12345);
        mockResponse.setProductName("测试产品");
        mockResponse.setImei1Mask("86123456****123");
        mockResponse.setStoreName("测试门店");

        // Mock服务返回
        when(imeiUploadService.queryImeiDetail(any(ImeiDetailQueryReq.class)))
                .thenReturn(new CommonApiResponse<>(mockResponse));

        // 执行测试
        CommonApiResponse<ImeiDetailQueryResp> response = soController.queryImeiDetail(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(12345, response.getData().getId());
        assertEquals("测试产品", response.getData().getProductName());
    }

    @Test
    void testQueryImeiSummary_Success() {
        // 准备测试数据
        ImeiSummaryQueryReq request = new ImeiSummaryQueryReq();
        request.setCountryCode("CN");
        request.setUserId("test-user-id");
        request.setMiId(123456789L);
        request.setUserTitle(500900001L);

        ImeiSummaryQueryResp mockResponse = new ImeiSummaryQueryResp();
        mockResponse.setTotalCount(100);
        mockResponse.setSuccessCount(80);
        mockResponse.setVeriftingCount(15);
        mockResponse.setFailedCount(5);

        // Mock服务返回
        when(imeiUploadService.queryImeiSummary(any(ImeiSummaryQueryReq.class)))
                .thenReturn(new CommonApiResponse<>(mockResponse));

        // 执行测试
        CommonApiResponse<ImeiSummaryQueryResp> response = soController.queryImeiSummary(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(100, response.getData().getTotalCount());
        assertEquals(80, response.getData().getSuccessCount());
        assertEquals(15, response.getData().getVeriftingCount());
        assertEquals(5, response.getData().getFailedCount());
    }

    @Test
    void testQueryImeiListByPage_ValidationError() {
        // 准备无效的测试数据
        ImeiListQueryReq request = new ImeiListQueryReq();
        // 缺少必填字段

        // Mock服务返回验证错误
        when(imeiUploadService.queryImeiListByPage(any(ImeiListQueryReq.class)))
                .thenReturn(new CommonApiResponse<>(400, "必填参数不能为空", null));

        // 执行测试
        CommonApiResponse<ImeiListQueryResp> response = soController.queryImeiListByPage(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("必填参数不能为空", response.getMessage());
        assertNull(response.getData());
    }
}
